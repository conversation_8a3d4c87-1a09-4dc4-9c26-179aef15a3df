#!/usr/bin/env python
# coding=utf-8

"""
@author: <PERSON><PERSON><PERSON>
@e-mail: ycz<PERSON>@smail.nju.edu.cn
@time: 2023/12/03 23:36
@org: Nanjing University
"""
import ase
from ase import io
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
from functools import partial
from multiprocessing import Pool


def splitlammpstrjfile(lammpstrj):
    '''

    :param lammpstrj:
    :return:
    '''
    print('opening trajctory file...')
    with open(lammpstrj, 'r') as foo:
        dataFile = foo.read().split('ITEM: TIMESTEP\n')
    print("spliting trajectory...")
    trj = []
    for iframe, frame in enumerate(dataFile[1:]):
        if iframe % 500 == 0:
            print('Frame No.: %d' % iframe)
        lines = frame.split('\n')[8:-1]
        config = []
        for line in lines:
            # id, type, ele, q, mass, xu, yu, zu = line.split()
            # config.append([int(id), ele, float(q), float(mass), np.array([float(xu), float(yu), float(zu)])])
            # id, type, ele, q, x, y, z, xu, yu, zu, mass = line.split()
            id, ele, q, mass, xu, yu, zu = line.split()
            config.append([int(id), ele, float(q), float(mass), np.array([float(xu), float(yu), float(zu)])])
        trj.append(config)

    print('split compelted\n')
    return trj


def splitxyz(xyztrj):
    '''
    the output is formatted as that of splitlammpstrjfile
    :param xyztrj:
    :return:
    '''
    xyz = io.read(xyztrj, ':')
    trj = []
    for frame in xyz:
        config = []
        for atom in frame:
            config.append([atom.index, atom.symbol, 0, atom.mass, np.array([atom.x, atom.y, atom.z])])
        trj.append(config)
    return trj


def lmp2atoms(trj: list, cell, pbc=(1, 1, 1)):
    '''
     convert lammpstrj to ase atoms object
     which facilitate the minimal distance calculation

    :param trj: lmptrj from splitlammpstrjfile
    :param cell:
    :param pbc:
    :return:
    '''
    print('converting lmptrj to ase.Atoms')
    xyz = []
    for frame in trj:
        frame = np.array(frame)
        pos = [list(x) for x in frame[::, 4]]
        atomtmp = ase.Atoms(symbols=frame[::, 1], cell=cell, pbc=pbc, positions=pos, charges=frame[::, 2],
                            masses=frame[::, 3])
        xyz.append(atomtmp)
    return xyz


def calcCN_singleFrame(iframe: int, xyz, centerAtom: list, pairAtom: list, rcut: float = 2.5):
    '''

    :param iframe:
    :param xyz:
    :param centerAtom:
    :param pairAtom:
    :param rcut:
    :return:
    '''
    cn_i = 0
    if iframe % 100 == 0:
        print('frame:', iframe)
    for icenter in centerAtom:
        for ipair in pairAtom:
            if xyz[iframe].get_distance(icenter, ipair, mic=True) <= rcut:
                cn_i += 1

    return cn_i / len(centerAtom)


def coordinationN(r, rc=2.75, nn=12, mm=24):
    try:
        return (1 - (r / rc) ** nn) / (1 - (r / rc) ** mm)
    except ZeroDivisionError:
        print('ZeroDivisionError, pass')
        return 0.5


def coordinationN1(r, r0=2.75, d0=1.0, nn=6, mm=12):
    return (1 - ((r - d0) / r0) ** nn) / (1 - ((r - d0) / r0) ** mm)


def fermi(r, rc=1.35, kappa=0.1):
    return 1.0 / (np.exp((r - rc) / kappa) + 1)


def calcCN_singleFrame_function1(iframe: int, xyz, centerAtom: list, pairAtom: list, rc: float = 3.0, nn=12, mm=24):
    '''

    :param iframe:
    :param xyz:
    :param centerAtom:
    :param pairAtom:
    :param rc:
    :param nn:
    :param mm:
    :return:
    '''
    cn_i = 0
    if iframe % 100 == 0:
        print('frame:', iframe)
    for icenter in centerAtom:
        for ipair in pairAtom:
            rxx = xyz[iframe].get_distance(icenter, ipair, mic=True)
            cn_i += coordinationN(rxx, rc=rc, nn=nn, mm=mm)

    return cn_i / len(centerAtom)


if __name__ == '__main__':
    NProcess = 8
    time0 = datetime.now()
    dirName = r'D:\simulationBackup\aluminum_hydroxides\goethite\simulation\110\110-4L-2\110-Y\pmf-cn-473k\1w-exchange\0p1-1/'
    print(dirName)
    lmptrjFileName = 'goe-pos-1.xyz'
    cmt = 'y-o'
    cell = [9.074, 10.975, 26.000, 90, 90, 90]
    # cell = [9.074,    9.233,   28.000, 90, 90, 90]

    # trj = splitlammpstrjfile(dirName + lmptrjFileName)
    trj = splitxyz(dirName + lmptrjFileName)
    print('time elapsed:', datetime.now() - time0)
    print('number of frames:', len(trj))
    print('number of atoms:', len(trj[0]))

    xyz = lmp2atoms(trj[::1], cell, pbc=(1, 1, 1))
    print('time elapsed:', datetime.now() - time0)

    y, o = [], []
    rcut = 3.0
    for atom in xyz[0]:
        if atom.symbol == 'Y':
            y.append(atom.index)
        if atom.symbol == 'O':
            o.append(atom.index)

    centerAtom, pairAtom = y, o

    calc1 = partial(calcCN_singleFrame_function1, xyz=xyz, centerAtom=centerAtom, pairAtom=pairAtom, rc=3.0, nn=12, mm=24)
    iframe = range(len(xyz))
    with Pool(NProcess) as p:
        cn_by_frame = p.map(calc1, iframe)
    # print(cn_by_frame)

    # unique = set(cn_by_frame).union()
    # unique = list(unique)
    # count_icn = [0 for i in unique]
    #
    # for icn in cn_by_frame:
    #     for xunique in unique:
    #         if icn == xunique:
    #             count_icn[unique.index(xunique)] += 1
    #
    # str1 = ''
    # for icn in unique:
    #     str1 += '%.1f: %d, %.2f%%\n' % (
    #         icn, count_icn[unique.index(icn)], count_icn[unique.index(icn)] / len(cn_by_frame) * 100)
    #
    # print(str1)

    plt.figure(0, figsize=(8, 6))
    plt.grid(True)
    plt.xlabel('Frames', fontsize=12)
    plt.ylabel('CN', fontsize=12)
    # plt.plot(cn_by_frame, 'b-', label=str1)
    plt.plot(cn_by_frame, 'b-')
    plt.legend(loc='best', fontsize='xx-large', edgecolor='None', facecolor='None')
    plt.tick_params(axis='both', which='major', width=1, labelsize='x-large', direction='in', bottom='on', top='on',
                    left='on', right='on')
    plt.tight_layout()

    figName = 'cn_by_frame-1-%s.png' % cmt
    plt.savefig(dirName + figName, format='png', dpi=300)
    plt.show()
    plt.close()
