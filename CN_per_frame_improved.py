#!/usr/bin/env python3
"""
配位数分析脚本 (改进版) - 使用HMJ工具库

Author: MengjiaHe <EMAIL> (基于<PERSON><PERSON><PERSON> Zhang的原始代码)
Date: 2025-07-07
Description: 分析分子动力学轨迹中的配位数随时间变化，支持多种计算方法和并行处理

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path
from datetime import datetime
import argparse
import logging

# 添加工具库路径
sys.path.append(str(Path(__file__).parent / 'hmj-py-lib'))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager
from md_analysis import MDAnalyzer
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CNAnalyzer:
    """配位数分析器类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配位数分析器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.md_analyzer = MDAnalyzer(self.config.get('md_analysis.n_processes', 8))
        self.plot_manager = PlotManager(self.config.get_plot_config())
        
        logger.info("🔬 配位数分析器初始化完成")
    
    def analyze_trajectory(self, 
                          trajectory_file: str,
                          file_type: str = 'xyz',
                          center_element: str = 'Y',
                          pair_element: str = 'O',
                          use_function: bool = True,
                          output_prefix: str = 'cn_analysis') -> dict:
        """
        分析轨迹文件中的配位数
        
        Args:
            trajectory_file: 轨迹文件路径
            file_type: 文件类型 ('xyz' 或 'lammps')
            center_element: 中心原子元素
            pair_element: 配对原子元素
            use_function: 是否使用函数计算配位数
            output_prefix: 输出文件前缀
            
        Returns:
            dict: 分析结果
        """
        start_time = datetime.now()
        logger.info(f"🚀 开始配位数分析: {trajectory_file}")
        
        try:
            # 1. 加载轨迹
            if file_type.lower() == 'xyz':
                trajectory = self.md_analyzer.split_xyz_trajectory(trajectory_file)
            elif file_type.lower() == 'lammps':
                trajectory = self.md_analyzer.split_lammps_trajectory(trajectory_file)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
            
            logger.info(f"📊 轨迹信息: {len(trajectory)} 帧, {len(trajectory[0])} 原子")
            
            # 2. 转换为ASE对象
            cell = self.config.get('md_analysis.cell_parameters', [9.074, 10.975, 26.000, 90, 90, 90])
            pbc = tuple(self.config.get('md_analysis.pbc', [True, True, True]))
            
            ase_trajectory = self.md_analyzer.trajectory_to_ase_atoms(trajectory, cell, pbc)
            
            # 3. 获取原子索引
            center_atoms = self.md_analyzer.get_atom_indices_by_symbol(ase_trajectory[0], center_element)
            pair_atoms = self.md_analyzer.get_atom_indices_by_symbol(ase_trajectory[0], pair_element)
            
            if not center_atoms:
                raise ValueError(f"未找到中心原子: {center_element}")
            if not pair_atoms:
                raise ValueError(f"未找到配对原子: {pair_element}")
            
            # 4. 计算配位数
            if use_function:
                cn_function = self.md_analyzer.coordination_function
                function_params = self.config.get('analysis_parameters.cn_calculation.function_parameters', {})
                cn_values = self.md_analyzer.calculate_cn_trajectory(
                    ase_trajectory, center_atoms, pair_atoms,
                    use_function=True, cn_function=cn_function, **function_params
                )
            else:
                cutoff = self.config.get('analysis_parameters.cn_calculation.cutoff_distance', 2.5)
                cn_values = self.md_analyzer.calculate_cn_trajectory(
                    ase_trajectory, center_atoms, pair_atoms, cutoff=cutoff
                )
            
            # 5. 创建时间轴
            md_config = self.config.get_md_config()
            time_data = DataLoader.create_time_axis(
                len(cn_values),
                timestep_fs=md_config.get('timestep_fs', 10.0),
                output_interval=md_config.get('output_interval', 0.5)
            )
            
            # 6. 绘制结果
            self._plot_results(time_data, cn_values, center_element, pair_element, output_prefix)
            
            # 7. 保存数据
            self._save_data(time_data, cn_values, f"{output_prefix}_data.txt")
            
            # 8. 统计分析
            stats = self._calculate_statistics(cn_values)
            
            elapsed_time = datetime.now() - start_time
            logger.info(f"✅ 配位数分析完成! 耗时: {elapsed_time}")
            
            return {
                'cn_values': cn_values,
                'time_data': time_data,
                'statistics': stats,
                'center_element': center_element,
                'pair_element': pair_element,
                'n_frames': len(cn_values),
                'elapsed_time': elapsed_time
            }
            
        except Exception as e:
            logger.error(f"❌ 配位数分析失败: {e}")
            raise
    
    def _plot_results(self, time_data, cn_values, center_element, pair_element, output_prefix):
        """绘制配位数结果"""
        try:
            fig = self.plot_manager.plot_distance_vs_time(
                time_data, cn_values,
                xlabel='Time (ps)',
                ylabel='Coordination Number',
                title=f'{center_element}-{pair_element} Coordination Number vs Time'
            )
            
            output_file = f"{output_prefix}_{center_element}-{pair_element}.svg"
            self.plot_manager.show_and_save(fig, output_file, show=False)
            
            logger.info(f"📊 配位数图形已保存: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ 绘图失败: {e}")
    
    def _save_data(self, time_data, cn_values, filename):
        """保存数据到文件"""
        try:
            data = np.column_stack((time_data, cn_values))
            np.savetxt(filename, data, header='Time(ps)\tCN', delimiter='\t', fmt='%.6f')
            logger.info(f"💾 数据已保存: {filename}")
            
        except Exception as e:
            logger.error(f"❌ 数据保存失败: {e}")
    
    def _calculate_statistics(self, cn_values):
        """计算统计信息"""
        stats = {
            'mean': np.mean(cn_values),
            'std': np.std(cn_values),
            'min': np.min(cn_values),
            'max': np.max(cn_values),
            'median': np.median(cn_values)
        }
        
        logger.info("📈 配位数统计:")
        logger.info(f"   平均值: {stats['mean']:.3f} ± {stats['std']:.3f}")
        logger.info(f"   范围: {stats['min']:.3f} - {stats['max']:.3f}")
        logger.info(f"   中位数: {stats['median']:.3f}")
        
        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='配位数分析工具 (改进版)')
    parser.add_argument('trajectory', help='轨迹文件路径')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--type', '-t', choices=['xyz', 'lammps'], default='xyz', help='文件类型')
    parser.add_argument('--center', default='Y', help='中心原子元素')
    parser.add_argument('--pair', default='O', help='配对原子元素')
    parser.add_argument('--function', action='store_true', help='使用函数计算配位数')
    parser.add_argument('--output', '-o', default='cn_analysis', help='输出文件前缀')
    
    args = parser.parse_args()
    
    try:
        # 创建分析器
        analyzer = CNAnalyzer(args.config)
        
        # 执行分析
        results = analyzer.analyze_trajectory(
            args.trajectory,
            file_type=args.type,
            center_element=args.center,
            pair_element=args.pair,
            use_function=args.function,
            output_prefix=args.output
        )
        
        print(f"\n🎉 分析完成!")
        print(f"   处理帧数: {results['n_frames']}")
        print(f"   平均配位数: {results['statistics']['mean']:.3f}")
        print(f"   耗时: {results['elapsed_time']}")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
