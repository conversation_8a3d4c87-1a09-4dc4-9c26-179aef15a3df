import matplotlib.pyplot as plt
import numpy as np

# 设置字体为 Helvetica
plt.rcParams['font.family'] = 'Helvetica'

# 读取数据文件
data_file = "ff28"  # 替换为你的数据文件名

# 截取前24000个数据点 12ps
step_limit = 24000

# 读取文件中的数据，每行一个数据点
with open(data_file, "r") as file:
    values = list(map(float, file.readlines()))  # 读取所有行并转换为浮点数

# 截取前24000个数据点
values = values[:step_limit]

# 假设每个数据点对应一个step，step = 1, 2, 3, ..., n
steps = np.arange(1, len(values) + 1)

# 将步骤转换为时间（ps），0.5 fs/step => 0.0000005 ps/step
ps = steps * 0.5 * 10**-3

# 计算累计平均值
cumulative_avg = np.cumsum(values) / np.arange(1, len(values) + 1)

# 绘制图形
plt.figure(figsize=(10, 6))

# 绘制每个数据点
plt.plot(ps, values, 'k-', label='large box, CN = 1.5 )', markersize=2)

# 绘制累计平均线
plt.plot(ps, cumulative_avg, 'r-', label='14.37‡0.55 kcal/moICN|')

# 设置标题和标签
plt.title('Data Points and Cumulative Average vs. Time (ps)')
plt.xlabel('Time (ps)')
plt.ylabel('Force(kcal/mol/Å)')
plt.legend()

# 设置x轴范围和刻度
x_range = (0, 13)
plt.xlim(x_range)
plt.xticks(np.arange(x_range[0], x_range[1] + 0.2, 2))

# 显示图形
plt.show()