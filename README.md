# HMJ Python脚本优化项目

## 项目概述

本项目对原有的分子动力学分析Python脚本进行了全面优化，创建了统一的工具库(`hmj-py-lib`)，提高了代码的可维护性、可重用性和易用性。

## 🚀 主要改进

### 1. 统一工具库 (`hmj-py-lib/`)
- **`data_loader.py`**: 统一数据加载接口，支持多种MD文件格式
- **`plotting.py`**: 集中绘图管理，统一样式和配置
- **`file_utils.py`**: 文件处理工具，包括批量操作和坐标转换
- **`md_analysis.py`**: 分子动力学分析工具，支持并行处理
- **`config_manager.py`**: 配置文件管理，支持YAML和JSON

### 2. 优化后的脚本
- **`CN_per_frame_improved.py`**: 配位数分析脚本
- **`distance_analysis_improved.py`**: 距离分析脚本
- **`plot_PMF_improved.py`**: PMF绘图脚本
- **`plot_RDF_CN_improved.py`**: RDF/CN绘图脚本
- **`coordinate_converter_improved.py`**: 坐标转换脚本

### 3. 配置管理
- **`config.yaml`**: 统一配置文件，包含所有脚本的参数设置

## 📦 安装和依赖

### 必需依赖
```bash
pip install numpy matplotlib ase pyyaml
```

### 可选依赖
```bash
pip install multiprocessing  # 并行处理支持
```

## 🔧 配置文件

编辑 `config.yaml` 文件来自定义参数：

```yaml
# 分子动力学分析配置
md_analysis:
  n_processes: 8                    # 并行处理进程数
  default_cutoff: 2.5               # 默认截断距离 (Å)
  timestep_fs: 10.0                 # 时间步长 (飞秒)

# 绘图配置
plotting:
  figure_size_cm: [8, 6]            # 图形大小 (厘米)
  dpi: 300                          # 分辨率
  font_size: 12                     # 字体大小

# 元素映射 (用于坐标转换)
file_processing:
  element_mappings:
    Fe1: Fe
    Fe2: Al
```

## 📖 使用指南

### 1. 配位数分析

```bash
# 基本用法
python CN_per_frame_improved.py trajectory.xyz

# 自定义参数
python CN_per_frame_improved.py trajectory.xyz \
  --center Y --pair O --function \
  --config config.yaml --output cn_analysis
```

### 2. 距离分析

```bash
# 单文件分析
python distance_analysis_improved.py Mo-O.dat

# 批量分析
python distance_analysis_improved.py data_directory/ \
  --batch --output-dir results/
```

### 3. PMF绘图

```bash
# 生成Word和PPT格式图形
python plot_PMF_improved.py PMF.dat --format both

# 比较多个PMF文件
python plot_PMF_improved.py PMF1.dat \
  --compare PMF1.dat PMF2.dat PMF3.dat
```

### 4. RDF/CN绘图

```bash
# 基本绘图
python plot_RDF_CN_improved.py Od1_rdfcn.dat

# 批量绘图
python plot_RDF_CN_improved.py data_directory/ \
  --batch --output-dir rdf_plots/
```

### 5. 坐标转换

```bash
# 单文件转换
python coordinate_converter_improved.py molecule.xyz \
  --mapping Fe1:Fe Fe2:Al

# 批量转换
python coordinate_converter_improved.py xyz_files/ \
  --pattern "*.xyz" --recursive

# 预演模式（不实际修改文件）
python coordinate_converter_improved.py xyz_files/ \
  --dry-run
```

## 🧪 测试

运行测试脚本验证工具库功能：

```bash
python test_hmj_lib.py
```

## 📁 文件结构

```
.
├── hmj-py-lib/                     # 工具库目录
│   ├── __init__.py                 # 模块初始化
│   ├── data_loader.py              # 数据加载器
│   ├── plotting.py                 # 绘图管理器
│   ├── file_utils.py               # 文件工具
│   ├── md_analysis.py              # MD分析器
│   └── config_manager.py           # 配置管理器
├── config.yaml                     # 配置文件
├── CN_per_frame_improved.py        # 配位数分析脚本
├── distance_analysis_improved.py   # 距离分析脚本
├── plot_PMF_improved.py            # PMF绘图脚本
├── plot_RDF_CN_improved.py         # RDF/CN绘图脚本
├── coordinate_converter_improved.py # 坐标转换脚本
├── test_hmj_lib.py                 # 测试脚本
└── README.md                       # 说明文档
```

## 🔍 主要特性

### 1. 模块化设计
- 功能分离，易于维护和扩展
- 统一接口，减少代码重复
- 可重用组件，提高开发效率

### 2. 配置管理
- 集中配置，避免硬编码
- 支持嵌套配置和默认值
- 易于自定义和部署

### 3. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 优雅的错误恢复

### 4. 并行处理
- 支持多进程并行计算
- 自动负载均衡
- 可配置进程数

### 5. 批量操作
- 支持批量文件处理
- 进度跟踪和错误报告
- 灵活的文件匹配模式

## 🎯 性能优化

1. **并行处理**: 配位数计算支持多进程并行
2. **内存优化**: 大文件分块处理，避免内存溢出
3. **缓存机制**: 重复计算结果缓存
4. **向量化计算**: 使用NumPy向量化操作

## 🛠️ 故障排除

### 常见问题

1. **导入错误**: 确保工具库路径正确添加到Python路径
2. **配置文件错误**: 检查YAML语法和文件路径
3. **内存不足**: 减少并行进程数或使用分块处理
4. **文件格式错误**: 验证输入文件格式是否正确

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 未来改进

1. **GUI界面**: 开发图形用户界面
2. **更多文件格式**: 支持更多MD文件格式
3. **云计算支持**: 支持云端并行计算
4. **机器学习集成**: 集成ML模型进行预测分析

## 📞 联系信息

- **作者**: MengjiaHe
- **邮箱**: <EMAIL>
- **日期**: 2025-07-07

## 📄 许可证

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
