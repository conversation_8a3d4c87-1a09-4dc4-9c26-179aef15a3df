import MDAnalysis as mda
import numpy as np
import matplotlib.pyplot as plt  # 导入matplotlib用于绘图

# 加载 .xyz 文件
u = mda.Universe('/Users/<USER>/Documents/My_Library/inp/hmj_inp/3-UO2_AsV-20190424/2As_PMF_C1/Distance-U-As-32-53/38.xyz')

# 设置 Fe 和 O 的索引
fe_index = 5   # Fe 原子索引
o_index = 126  # O 原子索引

# 获取 Fe 和 O 的原子
fe_atom = u.select_atoms(f"index {fe_index}")
o_atom = u.select_atoms(f"index {o_index}")

# 设置盒子大小（12.43 Å 立方体盒子）
box_size = 12.43  # 单位：Å

# 计算 Fe 和 O 之间的距离，考虑 PBC（周期性边界条件）
distances = []
for ts in u.trajectory:
    # 获取 Fe 和 O 的位置
    fe_pos = fe_atom.positions[0]
    o_pos = o_atom.positions[0]
    
    # 计算原子之间的距离（考虑 PBC）
    diff = fe_pos - o_pos
    diff -= np.round(diff / box_size) * box_size  # PBC 处理，调整为最短距离
    distance = np.linalg.norm(diff)  # 计算最短距离
    distances.append(distance)

# 保存结果到文件
np.savetxt("Fe_O_distances.txt", distances)

print("Fe-O 距离已保存到 Fe_O_distances.txt")

# 绘制 Fe-O 距离随时间变化的图
time_steps = np.arange(0, len(distances) * 0.0005*20 , 0.0005*20)  # 时间步长为 0.5 fs

plt.plot(time_steps, distances)
plt.xlabel("Time (ps)")
plt.ylabel("Fe-O Distance (Å)")
plt.title("Fe-O Distance Over Time")
plt.grid(False)
plt.show()