'''
Author: MengjiaHe <EMAIL>
Date: 2023-11-10 16:43:10
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2024-05-28 14:09:41
FilePath: /undefined/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v1-H0/bi/6cn/free/cn_TIME.py
Description: 

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import os
import numpy as np
import matplotlib
import matplotlib.pyplot as plt

# Load data
F = np.loadtxt('cn-vmd.dat')
x = np.arange(len(F)) 

# 设置 A4 纸张大小
paper_width = 210 / 25.4 / 1.1 /2.5  # 宽度，单位为英寸 1/3的A4
paper_height = 297 / 25.4 / 2/2.5  # 高度，单位为英寸

# 设置图形的大小和位置
fig, ax = plt.subplots(figsize=(paper_width, paper_height))
fig.subplots_adjust(bottom=0.15, right=0.95, top=0.9, left=0.1)

# 绘制图形
# ax.plot(E[:, 0] * 10 * 0.5 / 1000, E[:, 1], 'r', linewidth=1, label='E Data')
ax.plot(x * 10 * 0.5 / 1000, F, 'k', linewidth=1, label='F Data')
ax.set_xlim(0, 8)
ax.set_ylim(4.5, 6.5)

# ax.tick_params(axis='both', which='both', direction='in', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)
ax.tick_params(axis='both', which='both', direction='out', bottom=True, top=True, left=True, right=True, labelbottom=True, labeltop=False, labelleft=True, labelright=False, pad=5)

# ax.tick_params(axis='both', which='both', direction='in', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)

# 设置坐标轴标签
Ang = 'Å'
ax.set_xlabel('Time (ps)')
ax.set_ylabel(r'CN of W-O')
# ax.set_ylabel(r'W-O$_{\mathrm{H2O}}$ Distance ($\AA$)')
# (f'W-O\mathrm{H_2O} Distance ({Ang})')

# 设置图例
# ax.legend()

# 显示网格
# ax.grid(True)

# 保存图形
# plt.savefig('figure_test.png', format='png', dpi=300)
plt.rcParams['font.size'] = 12
plt.rcParams['font.sans-serif'] = 'Helvetica'
plt.rcParams['svg.fonttype'] = 'none'
plt.savefig('figure_test-cn.svg', bbox_inches='tight', format='svg')



# 显示图形
plt.show()
