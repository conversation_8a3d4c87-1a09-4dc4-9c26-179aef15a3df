# HMJ Python脚本配置文件
# 用于统一管理所有脚本的参数和设置

# 路径配置
paths:
  data_dir: ./data
  output_dir: ./output
  temp_dir: ./temp
  backup_dir: ./backup

# 分子动力学分析配置
md_analysis:
  n_processes: 8                    # 并行处理进程数
  default_cutoff: 2.5               # 默认截断距离 (Å)
  timestep_fs: 10.0                 # 时间步长 (飞秒)
  output_interval: 0.5              # 输出间隔 (以时间步为单位)
  
  # 晶胞参数 [a, b, c, alpha, beta, gamma]
  cell_parameters: [9.074, 10.975, 26.000, 90, 90, 90]
  
  # 周期性边界条件 [x, y, z]
  pbc: [true, true, true]

# 绘图配置
plotting:
  figure_size_cm: [8, 6]            # 图形大小 (厘米)
  dpi: 300                          # 分辨率
  font_family: sans-serif           # 字体族
  font_name: Helvetica              # 字体名称
  font_size: 12                     # 字体大小
  line_width: 1.0                   # 线条宽度
  marker_size: 2.5                  # 标记大小
  save_format: svg                  # 保存格式
  
  # 颜色配置
  colors:
    - black
    - red
    - blue
    - green
    - orange
  
  # 线型配置
  line_styles:
    - '-'
    - '--'
    - '-.'
    - ':'

# 文件处理配置
file_processing:
  backup_files: true                # 是否备份原文件
  backup_suffix: .bak               # 备份文件后缀
  encoding: utf-8                   # 文件编码
  
  # 元素映射 (用于坐标转换)
  element_mappings:
    Fe1: Fe
    Fe2: Al

# 分析参数配置
analysis_parameters:
  # 配位数计算参数
  cn_calculation:
    cutoff_distance: 2.5            # 截断距离
    function_parameters:
      rc: 3.0                       # 函数参数rc
      nn: 12                        # 函数参数n
      mm: 24                        # 函数参数m
  
  # RDF计算参数
  rdf_parameters:
    r_max: 8.0                      # 最大距离
    dr: 0.1                         # 距离间隔
  
  # PMF绘图参数
  pmf_plotting:
    x_range: [1, 0.1]               # x轴范围
    x_tick_interval: 0.2            # x轴刻度间隔
    invert_x: true                  # 是否反转x轴
  
  # RDF/CN绘图参数
  rdf_cn_plotting:
    x_range: [2, 4.7]               # x轴范围
    rdf_ylim: [0, 3]                # RDF y轴范围
    cn_ylim: [0, 4]                 # CN y轴范围

# 特定脚本配置
scripts:
  # CN_per_frame脚本配置
  cn_per_frame:
    center_element: Y               # 中心原子元素
    pair_element: O                 # 配对原子元素
    use_function: true              # 是否使用函数计算
    
  # 距离分析脚本配置
  distance_analysis:
    default_data_file: Mo-O.dat     # 默认数据文件
    time_range: [0, 12]             # 时间范围 (ps)
    time_tick_interval: 2           # 时间刻度间隔
    
  # 坐标转换脚本配置
  coordinate_conversion:
    input_pattern: "*.xyz"          # 输入文件模式
    output_suffix: "_converted"     # 输出文件后缀
    dry_run: false                  # 是否预演模式
