#!/usr/bin/env python3
"""
坐标转换脚本 (改进版) - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 批量转换XYZ文件中的原子元素符号，支持多种转换模式和备份功能

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path
import argparse
import logging
from typing import Dict, List, Optional

# 添加工具库路径
sys.path.append(str(Path(__file__).parent / 'hmj-py-lib'))

from file_utils import FileUtils
from config_manager import ConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CoordinateConverter:
    """坐标转换器类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化坐标转换器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.file_utils = FileUtils()
        
        # 获取默认元素映射
        self.default_mappings = self.config.get('file_processing.element_mappings', {})
        
        logger.info("🔄 坐标转换器初始化完成")
    
    def convert_single_file(self, 
                           filepath: str,
                           element_map: Dict[str, str],
                           output_suffix: str = "_converted",
                           backup: bool = True,
                           dry_run: bool = False) -> Optional[Path]:
        """
        转换单个XYZ文件
        
        Args:
            filepath: 输入文件路径
            element_map: 元素映射字典
            output_suffix: 输出文件后缀
            backup: 是否备份原文件
            dry_run: 是否预演模式
            
        Returns:
            Path: 输出文件路径，失败时返回None
        """
        logger.info(f"🔄 转换文件: {Path(filepath).name}")
        
        try:
            if dry_run:
                logger.info("🔍 预演模式 - 不会实际修改文件")
                # 在预演模式下，只检查文件和映射
                self._preview_conversion(filepath, element_map)
                return None
            
            # 执行转换
            output_path = self.file_utils.convert_xyz_elements(
                filepath, element_map, output_suffix
            )
            
            if output_path and backup:
                # 创建备份
                backup_suffix = self.config.get('file_processing.backup_suffix', '.bak')
                backup_path = Path(filepath).with_suffix(Path(filepath).suffix + backup_suffix)
                self.file_utils.backup_file(filepath, str(backup_path))
                logger.info(f"💾 备份文件: {backup_path.name}")
            
            if output_path:
                logger.info(f"✅ 转换成功: {output_path.name}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"❌ 转换失败: {Path(filepath).name} - {e}")
            return None
    
    def _preview_conversion(self, filepath: str, element_map: Dict[str, str]):
        """预览转换结果"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            conversions = {}
            for i, line in enumerate(lines[2:], start=3):  # 跳过前两行
                parts = line.strip().split()
                if len(parts) >= 4:
                    element = parts[0]
                    if element in element_map:
                        new_element = element_map[element]
                        if element != new_element:
                            if element not in conversions:
                                conversions[element] = {'new': new_element, 'count': 0, 'lines': []}
                            conversions[element]['count'] += 1
                            conversions[element]['lines'].append(i)
            
            if conversions:
                logger.info("🔍 预览转换:")
                for old_elem, info in conversions.items():
                    logger.info(f"   {old_elem} → {info['new']}: {info['count']} 个原子")
            else:
                logger.info("🔍 预览: 无需转换")
                
        except Exception as e:
            logger.error(f"❌ 预览失败: {e}")
    
    def batch_convert(self, 
                     input_dir: str,
                     element_map: Dict[str, str] = None,
                     pattern: str = "*.xyz",
                     output_suffix: str = "_converted",
                     backup: bool = True,
                     dry_run: bool = False,
                     recursive: bool = False) -> Dict:
        """
        批量转换XYZ文件
        
        Args:
            input_dir: 输入目录
            element_map: 元素映射字典
            pattern: 文件匹配模式
            output_suffix: 输出文件后缀
            backup: 是否备份原文件
            dry_run: 是否预演模式
            recursive: 是否递归搜索
            
        Returns:
            Dict: 批量转换结果
        """
        logger.info(f"🔄 开始批量转换: {input_dir}")
        
        # 使用默认映射或提供的映射
        if element_map is None:
            element_map = self.default_mappings
        
        if not element_map:
            raise ValueError("未提供元素映射")
        
        # 查找文件
        input_path = Path(input_dir)
        xyz_files = self.file_utils.find_files(input_path, pattern, recursive)
        
        if not xyz_files:
            logger.warning(f"⚠️ 未找到匹配的文件: {pattern}")
            return {'success_count': 0, 'failed_count': 0, 'results': {}, 'failed_files': []}
        
        logger.info(f"📁 找到 {len(xyz_files)} 个文件")
        
        # 批量转换
        results = {}
        failed_files = []
        
        for i, xyz_file in enumerate(xyz_files, 1):
            logger.info(f"📁 处理文件 {i}/{len(xyz_files)}: {xyz_file.name}")
            
            try:
                output_path = self.convert_single_file(
                    str(xyz_file),
                    element_map,
                    output_suffix,
                    backup,
                    dry_run
                )
                
                results[str(xyz_file)] = {
                    'output_path': str(output_path) if output_path else None,
                    'success': output_path is not None or dry_run
                }
                
            except Exception as e:
                logger.error(f"❌ 文件处理失败: {xyz_file.name} - {e}")
                failed_files.append(str(xyz_file))
                results[str(xyz_file)] = {
                    'output_path': None,
                    'success': False,
                    'error': str(e)
                }
        
        success_count = sum(1 for r in results.values() if r['success'])
        failed_count = len(failed_files)
        
        logger.info(f"📊 批量转换完成: 成功 {success_count}, 失败 {failed_count}")
        
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'results': results,
            'failed_files': failed_files,
            'element_map': element_map
        }
    
    def create_element_mapping(self, mapping_pairs: List[str]) -> Dict[str, str]:
        """
        从字符串列表创建元素映射
        
        Args:
            mapping_pairs: 映射对列表，格式为 ["old1:new1", "old2:new2", ...]
            
        Returns:
            Dict: 元素映射字典
        """
        element_map = {}
        
        for pair in mapping_pairs:
            if ':' not in pair:
                logger.warning(f"⚠️ 忽略无效映射: {pair}")
                continue
            
            old_elem, new_elem = pair.split(':', 1)
            old_elem = old_elem.strip()
            new_elem = new_elem.strip()
            
            if old_elem and new_elem:
                element_map[old_elem] = new_elem
                logger.info(f"🔄 添加映射: {old_elem} → {new_elem}")
            else:
                logger.warning(f"⚠️ 忽略空映射: {pair}")
        
        return element_map
    
    def validate_xyz_file(self, filepath: str) -> bool:
        """
        验证XYZ文件格式
        
        Args:
            filepath: 文件路径
            
        Returns:
            bool: 是否为有效的XYZ文件
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 3:
                return False
            
            # 检查第一行是否为原子数
            try:
                n_atoms = int(lines[0].strip())
            except ValueError:
                return False
            
            # 检查原子数据行数是否匹配
            if len(lines) < n_atoms + 2:
                return False
            
            # 检查原子数据格式
            for i in range(2, min(5, len(lines))):  # 只检查前几行
                parts = lines[i].strip().split()
                if len(parts) < 4:
                    return False
                
                # 检查坐标是否为数字
                try:
                    float(parts[1])
                    float(parts[2])
                    float(parts[3])
                except ValueError:
                    return False
            
            return True
            
        except Exception:
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='坐标转换工具 (改进版)')
    parser.add_argument('input_path', help='输入文件或目录路径')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--mapping', '-m', nargs='+', help='元素映射 (格式: old1:new1 old2:new2)')
    parser.add_argument('--pattern', '-p', default='*.xyz', help='文件匹配模式')
    parser.add_argument('--suffix', '-s', default='_converted', help='输出文件后缀')
    parser.add_argument('--no-backup', action='store_true', help='不备份原文件')
    parser.add_argument('--dry-run', action='store_true', help='预演模式，不实际修改文件')
    parser.add_argument('--recursive', '-r', action='store_true', help='递归搜索子目录')
    
    args = parser.parse_args()
    
    try:
        # 创建转换器
        converter = CoordinateConverter(args.config)
        
        # 创建元素映射
        element_map = None
        if args.mapping:
            element_map = converter.create_element_mapping(args.mapping)
        
        input_path = Path(args.input_path)
        
        if input_path.is_file():
            # 单文件模式
            if not converter.validate_xyz_file(str(input_path)):
                logger.error("❌ 输入文件不是有效的XYZ文件")
                sys.exit(1)
            
            if element_map is None:
                element_map = converter.default_mappings
            
            if not element_map:
                logger.error("❌ 未提供元素映射")
                sys.exit(1)
            
            output_path = converter.convert_single_file(
                str(input_path),
                element_map,
                args.suffix,
                not args.no_backup,
                args.dry_run
            )
            
            if not args.dry_run:
                if output_path:
                    print(f"\n🎉 转换完成!")
                    print(f"   输入文件: {input_path.name}")
                    print(f"   输出文件: {output_path.name}")
                else:
                    print(f"\n❌ 转换失败!")
            else:
                print(f"\n🔍 预演完成!")
        
        elif input_path.is_dir():
            # 批量模式
            results = converter.batch_convert(
                str(input_path),
                element_map,
                args.pattern,
                args.suffix,
                not args.no_backup,
                args.dry_run,
                args.recursive
            )
            
            if not args.dry_run:
                print(f"\n🎉 批量转换完成!")
                print(f"   成功转换: {results['success_count']} 个文件")
                print(f"   转换失败: {results['failed_count']} 个文件")
            else:
                print(f"\n🔍 批量预演完成!")
                print(f"   检查文件: {len(results['results'])} 个")
            
            if results['failed_files']:
                print(f"   失败文件: {', '.join([Path(f).name for f in results['failed_files']])}")
        
        else:
            logger.error("❌ 输入路径不存在")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
