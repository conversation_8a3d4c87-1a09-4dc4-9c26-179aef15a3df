'''
Author: MengjiaHe <EMAIL>
Date: 2023-11-10 16:43:10
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2023-11-28 16:08:56
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v2-1H/bi/v2-6cn/free/dis_W-O.py
Description: 

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import numpy as np
import matplotlib.pyplot as plt

# Load data
F = np.loadtxt('1.dat')
E = np.loadtxt('2.dat')

# 创建一个 8x6 厘米大小的图
plt.figure(figsize=(8/2.54, 8/2.54))
plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
plt.rcParams['svg.fonttype'] = 'none'

# 绘制图形
plt.plot(E[:, 0] * 10 * 0.5 / 1000, E[:, 1], 'r', linewidth=1, label='E Data')
plt.plot(F[:, 0] * 10 * 0.5 / 1000, F[:, 1], 'k', linewidth=1, label='F Data')

# 设置坐标轴标签
Ang = 'Å'
plt.xlabel('Time (ps)')
plt.ylabel(f'W-Owater Distance ({Ang})')

# 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
plt.xlim(0, 14)
plt.xticks(np.arange(0, 15, 2)) # 9 表示结束值（不包含在内）
# plt.xticks(np.arange(0, 9, 2), labels=[str(i) for i in np.arange(0, 9, 2)])  # 设置刻度位置和标签


plt.ylim(2, 7)
# plt.xticks(np.arange(2, 9, 2))  # 设置刻度为 2 到 8，间隔为 2
# 保存图形
plt.savefig('6-coor-dis-W-O.svg', bbox_inches='tight', format='svg')

# 显示图形
plt.show()
