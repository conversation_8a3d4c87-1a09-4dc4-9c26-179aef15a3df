'''
Author: MengjiaHe <EMAIL>
Date: 2023-11-10 16:43:10
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-06-04 10:59:30
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v1-H0/bi/6cn/free/dis_oh-2.5.py
Description: 

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import os
import numpy as np
import matplotlib
import matplotlib.pyplot as plt

# 设置正确的文件路径
dirName = r'/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/Mo/v1-H0/v2-6cn-5cn/'

# Load data
F = np.loadtxt(os.path.join(dirName, 'Mo-Ow1.dat'))
E = np.loadtxt(os.path.join(dirName, 'Mo-Ow2.dat'))
A = np.loadtxt(os.path.join(dirName, 'cn-vmd.dat'))
X = E[:, 0] * 10 * 0.5 / 1000

# 设置 A4 纸张大小
paper_width = 210 / 25.4 / 1.1 /2.5  # 宽度，单位为英寸 1/3的A4
paper_height = 297 / 25.4 / 2/2.5  # 高度，单位为英寸

# 设置图形的大小和位置
fig, ax1 = plt.subplots(figsize=(paper_width, paper_height))
fig.subplots_adjust(bottom=0.15, right=0.95, top=0.9, left=0.1)

# 绘制图形
ax1.plot(X, E[:, 1], 'k', linewidth=1, label='E Data')
ax1.plot(X, F[:, 1], 'k', linewidth=1, label='F Data')
ax1.set_xlim(0, 12)
ax1.set_ylim(2.0, 7.0)

ax2 = ax1.twinx()

ax2.plot(X,A,'b', linewidth=1, label='CN Data')
ax2.set_ylim(3, 7)

# 设置右侧 y 轴刻度和标签颜色为蓝色
ax2.tick_params(axis='y', colors='b')
ax2.yaxis.label.set_color('b')

# 手动设置刻度标签颜色为蓝色
for label in ax2.get_yticklabels():
    label.set_color('b')


# ax.tick_params(axis='both', which='both', direction='in', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)
ax1.tick_params(axis='both', which='both', direction='out', bottom=True, top=False, left=True, right=False, labelbottom=True, labeltop=False, labelleft=True, labelright=False, pad=5)

# ax.tick_params(axis='both', which='both', direction='in', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)

# 设置坐标轴标签
Ang = 'Å'
ax1.set_xlabel('Time (ps)')
ax1.set_ylabel(r'Distance of Mo-O$_{\mathrm{water}}$ ($\AA$)')
ax2.set_ylabel(r'CN of Mo-O')

# (f'Mo-O\mathrm{H_2O} Distance ({Ang})')

# 设置图例
# ax.legend()

# 显示网格
# ax.grid(True)

# 保存图形
plt.rcParams['font.size'] = 12
plt.rcParams['font.sans-serif'] = 'Helvetica'
plt.rcParams['svg.fonttype'] = 'none'
output_path = os.path.join(dirName, 'figure_test-2.5.svg')
plt.savefig(output_path, bbox_inches='tight', format='svg', dpi=300)
print(f"图形已保存到: {output_path}")



# 显示图形
plt.show()
