#!/usr/bin/env python3
"""
距离分析脚本 (改进版) - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 分析和可视化原子间距离随分子动力学模拟时间的变化，支持多种数据格式和自定义参数

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path
import argparse
import logging
import numpy as np

# 添加工具库路径
sys.path.append(str(Path(__file__).parent / 'hmj-py-lib'))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DistanceAnalyzer:
    """距离分析器类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化距离分析器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.data_loader = DataLoader()
        self.plot_manager = PlotManager(self.config.get_plot_config())
        
        logger.info("📏 距离分析器初始化完成")
    
    def analyze_distance_file(self, 
                             data_file: str,
                             output_file: str = None,
                             title: str = None,
                             x_range: tuple = None,
                             x_tick_interval: float = None) -> dict:
        """
        分析距离数据文件
        
        Args:
            data_file: 距离数据文件路径
            output_file: 输出图形文件路径
            title: 图形标题
            x_range: x轴范围
            x_tick_interval: x轴刻度间隔
            
        Returns:
            dict: 分析结果
        """
        logger.info(f"🚀 开始距离分析: {data_file}")
        
        try:
            # 1. 加载距离数据
            distance_data = self.data_loader.load_distance_data(data_file)
            
            # 验证数据
            if not self.data_loader.validate_data(distance_data, check_finite=True):
                raise ValueError("距离数据验证失败")
            
            # 2. 创建时间轴
            md_config = self.config.get_md_config()
            time_data = self.data_loader.create_time_axis(
                len(distance_data),
                timestep_fs=md_config.get('timestep_fs', 10.0),
                output_interval=md_config.get('output_interval', 0.5)
            )
            
            # 3. 设置默认参数
            if output_file is None:
                output_file = Path(data_file).with_suffix('.svg').name
            
            if title is None:
                title = f"Distance Analysis - {Path(data_file).stem}"
            
            # 从配置文件获取默认范围
            script_config = self.config.get('scripts.distance_analysis', {})
            if x_range is None:
                time_range = script_config.get('time_range', [0, 12])
                x_range = (time_range[0], min(time_range[1], time_data[-1]))
            
            if x_tick_interval is None:
                x_tick_interval = script_config.get('time_tick_interval', 2)
            
            # 4. 绘制图形
            fig = self.plot_manager.plot_distance_vs_time(
                time_data, distance_data,
                xlabel='Time (ps)',
                ylabel='Distance (Å)',
                title=title
            )
            
            # 5. 自定义坐标轴
            self._customize_axes(x_range, x_tick_interval)
            
            # 6. 保存和显示
            self.plot_manager.show_and_save(fig, output_file, show=True)
            
            # 7. 计算统计信息
            stats = self._calculate_statistics(distance_data, time_data)
            
            logger.info("✅ 距离分析完成!")
            
            return {
                'distance_data': distance_data,
                'time_data': time_data,
                'statistics': stats,
                'output_file': output_file
            }
            
        except Exception as e:
            logger.error(f"❌ 距离分析失败: {e}")
            raise
    
    def _customize_axes(self, x_range, x_tick_interval):
        """自定义坐标轴"""
        import matplotlib.pyplot as plt
        
        # 设置x轴范围和刻度
        plt.xlim(x_range)
        x_ticks = np.arange(x_range[0], x_range[1] + 1, x_tick_interval)
        plt.xticks(x_ticks)
        
        logger.info(f"📊 坐标轴设置: x范围 {x_range}, 刻度间隔 {x_tick_interval}")
    
    def _calculate_statistics(self, distance_data, time_data):
        """计算统计信息"""
        stats = {
            'mean_distance': np.mean(distance_data),
            'std_distance': np.std(distance_data),
            'min_distance': np.min(distance_data),
            'max_distance': np.max(distance_data),
            'median_distance': np.median(distance_data),
            'time_range': (time_data[0], time_data[-1]),
            'n_points': len(distance_data)
        }
        
        logger.info("📈 距离统计:")
        logger.info(f"   平均距离: {stats['mean_distance']:.3f} ± {stats['std_distance']:.3f} Å")
        logger.info(f"   距离范围: {stats['min_distance']:.3f} - {stats['max_distance']:.3f} Å")
        logger.info(f"   中位数: {stats['median_distance']:.3f} Å")
        logger.info(f"   时间范围: {stats['time_range'][0]:.2f} - {stats['time_range'][1]:.2f} ps")
        logger.info(f"   数据点数: {stats['n_points']}")
        
        return stats
    
    def batch_analyze(self, data_files: list, output_dir: str = None) -> dict:
        """
        批量分析多个距离文件
        
        Args:
            data_files: 距离数据文件列表
            output_dir: 输出目录
            
        Returns:
            dict: 批量分析结果
        """
        logger.info(f"🔄 开始批量距离分析: {len(data_files)} 个文件")
        
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
        
        results = {}
        failed_files = []
        
        for i, data_file in enumerate(data_files, 1):
            try:
                logger.info(f"📁 处理文件 {i}/{len(data_files)}: {Path(data_file).name}")
                
                # 设置输出文件路径
                if output_dir:
                    output_file = output_path / f"{Path(data_file).stem}_analysis.svg"
                else:
                    output_file = None
                
                # 执行分析
                result = self.analyze_distance_file(
                    data_file, 
                    str(output_file) if output_file else None,
                    title=f"Distance Analysis - {Path(data_file).stem}"
                )
                
                results[data_file] = result
                logger.info(f"✅ 文件处理成功: {Path(data_file).name}")
                
            except Exception as e:
                logger.error(f"❌ 文件处理失败: {Path(data_file).name} - {e}")
                failed_files.append(data_file)
        
        logger.info(f"📊 批量分析完成: 成功 {len(results)}, 失败 {len(failed_files)}")
        
        return {
            'results': results,
            'failed_files': failed_files,
            'success_count': len(results),
            'failed_count': len(failed_files)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='距离分析工具 (改进版)')
    parser.add_argument('data_file', help='距离数据文件路径')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--output', '-o', help='输出图形文件路径')
    parser.add_argument('--title', '-t', help='图形标题')
    parser.add_argument('--x-range', nargs=2, type=float, help='x轴范围')
    parser.add_argument('--x-interval', type=float, help='x轴刻度间隔')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    parser.add_argument('--output-dir', help='批量处理输出目录')
    
    args = parser.parse_args()
    
    try:
        # 创建分析器
        analyzer = DistanceAnalyzer(args.config)
        
        if args.batch:
            # 批量处理模式
            from hmj_py_lib.file_utils import FileUtils
            
            data_files = FileUtils.find_files(
                Path(args.data_file).parent,
                pattern="*.dat",
                recursive=False
            )
            
            if not data_files:
                logger.error("❌ 未找到数据文件")
                sys.exit(1)
            
            results = analyzer.batch_analyze(
                [str(f) for f in data_files],
                args.output_dir
            )
            
            print(f"\n🎉 批量分析完成!")
            print(f"   成功处理: {results['success_count']} 个文件")
            print(f"   失败: {results['failed_count']} 个文件")
            
        else:
            # 单文件处理模式
            results = analyzer.analyze_distance_file(
                args.data_file,
                output_file=args.output,
                title=args.title,
                x_range=tuple(args.x_range) if args.x_range else None,
                x_tick_interval=args.x_interval
            )
            
            print(f"\n🎉 分析完成!")
            print(f"   平均距离: {results['statistics']['mean_distance']:.3f} Å")
            print(f"   数据点数: {results['statistics']['n_points']}")
            print(f"   输出文件: {results['output_file']}")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
