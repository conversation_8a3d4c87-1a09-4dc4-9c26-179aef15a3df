`'''
Author: MengjiaHe <EMAIL>
Date: 2019-02-14 13:20:17
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2023-10-20 15:11:40
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Matlab_hmj/f_Distance.py
Description: For PMF calculation

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['axes.prop_cycle'] = matplotlib.cycler(color=['black'])
plt.rcParams['font.sans-serif'] = 'Helvetica'

# 获取当前脚本所在目录
script_dir = os.path.dirname(os.path.realpath(__file__))

# 切换工作目录到脚本所在目录
os.chdir(script_dir)

# Load force data
F = np.loadtxt('ff-m11')

# Calculate average forces
data = F[6:]
mean_force = np.mean(data)
half_length = len(data) // 2
abs_diff = 0.5 * abs(np.mean(data[:half_length]) - np.mean(data[half_length:]))

# Calculate running averages
running_avg = np.cumsum(data) / (np.arange(len(data)) + 1)


# Set up figure and axis
fig, axs = plt.subplots(2, 1, figsize=(8, 6))
fig.suptitle('Title of Your Plot', fontsize=16)

# Plot running average
axs[0].plot(running_avg, color='black', label='Running Average')
axs[0].set_ylabel('Y-axis Label')
axs[0].legend(fontsize=12)

# Plot raw data
axs[1].plot(data, color='black', label='Raw Data')
axs[1].set_xlabel('X-axis Label')
axs[1].set_ylabel('Y-axis Label')
axs[1].legend(fontsize=12)

# Adjust layout
plt.tight_layout(rect=[0, 0, 1, 0.96])

# Save the figure
plt.savefig('plot.png', dpi=300, bbox_inches='tight')

# Display the plot
plt.show()
