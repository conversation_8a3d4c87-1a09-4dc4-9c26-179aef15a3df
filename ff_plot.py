'''
Author: MengjiaHe <EMAIL>
Date: 2025-01-15 17:07:05
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-01-23 21:40:08
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v1-H0/bi/pmf-4cn-dis/b-mono-no/42/ff_plot.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import matplotlib.pyplot as plt
import numpy as np

# 读取数据文件
data_file = "ff42_new"  # 替换为你的数据文件名

# 读取文件中的数据，每行一个数据点
with open(data_file, "r") as file:
    values = list(map(float, file.readlines()))  # 读取所有行并转换为浮点数

# 瞬时数据的时间轴（从第一个点开始）
steps = np.arange(1, len(values) + 1)
ps = steps * 0.5 * 10**-3  # 0.5 fs/step => 0.0000005 ps/step

# 计算累计平均值（从第 24000 个数据点开始）
start_index = 17000
if len(values) > start_index:
    subset_values = values[start_index:]  # 取从第 24000 号数据点开始的部分
    cumulative_avg = np.cumsum(subset_values) / np.arange(1, len(subset_values) + 1)
    print(f"cumulative_avg: {cumulative_avg[-1]:.3f} kcal/mol/Å")
    # 计算统计误差（前后半部分的均值差 / 2）
    midpoint = len(subset_values) // 2
    first_half_mean = np.mean(subset_values[:midpoint])
    second_half_mean = np.mean(subset_values[midpoint:])
    error = abs(second_half_mean - first_half_mean) / 2  # 误差范围的一半
    
    print(f"First half mean: {first_half_mean:.3f} kcal/mol/Å")
    print(f"Second half mean: {second_half_mean:.3f} kcal/mol/Å")
    print(f"Estimated statistical error: {error:.3f} kcal/mol/Å")

    # 对应的时间轴（只计算累计平均值的部分）
    cumulative_steps = np.arange(start_index + 1, len(values) + 1)
    cumulative_ps = cumulative_steps * 0.5 * 10**-3


    # 绘图
    plt.figure(figsize=(10, 6))
    plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
    plt.rcParams['svg.fonttype'] = 'none'
    
    # 绘制瞬时力（Instantaneous Force）：从头开始
    plt.plot(ps, values, 'k-', label='Instantaneous Force', markersize=2)

    # 绘制累计平均力（Cumulative Average Force）：从 24000 号点开始
    plt.plot(cumulative_ps, cumulative_avg, 'r-', label=f'Cumulative Average Force)')

    # 设置标题和标签
    # plt.title('Force vs. Time (ps)')
    plt.xlabel('Time (ps)')
    plt.ylabel('Force (kcal/mol/Å)')
    plt.legend()
    # 设置 x 轴范围和刻度
    x_range = (0, ps[-1])  # 从 0 到最大时间
    plt.xlim(x_range)
    plt.xticks(np.arange(x_range[0], x_range[1] + 2, 2))
    plt.savefig('PMF42.svg', bbox_inches='tight', format='svg')

    # 显示图形
    plt.show()
else:
    print(f"数据点不足 {start_index}，无法计算累计平均值。")