'''
Author: MengjiaHe <EMAIL>
Date: 2025-07-07 21:34:12
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 21:41:47
FilePath: /newpKa1/generate_colvar.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
#!/usr/bin/env python3
"""
Generate repeated COLLECTIVE blocks with incrementing COLVAR numbers
"""

def generate_collective_blocks():
    """Generate 122 COLLECTIVE blocks with COLVAR numbers from 5 to 126"""
    
    template = """  &COLLECTIVE
       TARGET [angstrom] 1.0     
       INTERMOLECULAR
       COLVAR {colvar_num}
       &RESTRAINT
        k 0.1
       &END RESTRAINT
  &END COLLECTIVE
  """
    
    blocks = []
    
    # Start from COLVAR 4 and generate 122 blocks (61*2)
    for i in range(122):
        colvar_num = 4 + i  # Start from 4, so 4，5, 6, 7, ..., 126
        block = template.format(colvar_num=colvar_num)
        blocks.append(block)
    
    return '\n'.join(blocks)

if __name__ == "__main__":
    content = generate_collective_blocks()
    
    # Write to file
    with open("COLVAR_index.inc", "w") as f:
        f.write(content)
    
    print(f"Generated 122 COLLECTIVE blocks with COLVAR numbers from 4 to 125")
    print("Content saved to 'COLVAR_index.inc'")
    
    # Show first few and last few blocks as preview
    lines = content.split('\n')
    print(f"\nTotal lines: {len(lines)}")
    print("\nFirst block:")
    for line in lines[:8]:
        print(line)
    print("\nLast block:")
    for line in lines[-8:]:
        print(line)
