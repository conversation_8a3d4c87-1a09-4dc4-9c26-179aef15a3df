'''
Author: MengjiaH<PERSON> <EMAIL>
Date: 2025-07-07 21:44:16
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 21:45:00
FilePath: /newpKa1/generate_oh_colvar.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
#!/usr/bin/env python3
"""
Generate COLVAR blocks for O-H distances in water molecules
Water molecules are arranged as O-H-H cycles
61 water molecules * 2 O-H bonds each = 122 COLVAR blocks
"""

def generate_oh_colvar_blocks():
    """Generate COLVAR blocks for O-H distances"""
    
    template = """     &COLVAR
      &DISTANCE
       ATOMS {oxygen_idx} {hydrogen_idx}
      &END DISTANCE
     &END COLVAR"""
    
    blocks = []
    
    # For 61 water molecules in O-H-H pattern
    for water_mol in range(61):
        # Each water molecule starts at position: water_mol * 3
        # O atom is at position: water_mol * 3 + 1 (1-based indexing)
        # H1 atom is at position: water_mol * 3 + 2
        # H2 atom is at position: water_mol * 3 + 3
        
        oxygen_idx = water_mol * 3 + 1
        h1_idx = water_mol * 3 + 2
        h2_idx = water_mol * 3 + 3
        
        # Generate COLVAR for O-H1 bond
        block1 = template.format(oxygen_idx=oxygen_idx, hydrogen_idx=h1_idx)
        blocks.append(block1)
        
        # Generate COLVAR for O-H2 bond
        block2 = template.format(oxygen_idx=oxygen_idx, hydrogen_idx=h2_idx)
        blocks.append(block2)
    
    return '\n'.join(blocks)

if __name__ == "__main__":
    content = generate_oh_colvar_blocks()
    
    # Write to file
    with open("COLVAR.inc", "w") as f:
        f.write(content)
    
    print(f"Generated 122 COLVAR blocks for O-H distances")
    print("Content saved to 'COLVAR.inc'")
    
    # Show first few and last few blocks as preview
    lines = content.split('\n')
    print(f"\nTotal lines: {len(lines)}")
    print("\nFirst two COLVAR blocks (first water molecule):")
    for line in lines[:10]:
        print(line)
    print("\nLast two COLVAR blocks (last water molecule):")
    for line in lines[-10:]:
        print(line)
    
    # Show some statistics
    print(f"\nStatistics:")
    print(f"Water molecules: 61")
    print(f"O-H bonds per molecule: 2")
    print(f"Total COLVAR blocks: 122")
    print(f"Atom indices range: 1 to {61*3} (183 atoms total for water molecules)")
