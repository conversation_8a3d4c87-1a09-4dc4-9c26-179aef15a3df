"""
配置管理模块 - 提供统一的配置文件管理

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 配置文件管理器，支持YAML、JSON等格式的配置文件
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Union, Optional
import logging

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    logging.warning("PyYAML未安装，YAML配置文件功能将不可用")

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file) if config_file else None
        self.config = {}
        
        if self.config_file and self.config_file.exists():
            self.load_config()
        else:
            self._set_default_config()
        
        logger.info("⚙️ 配置管理器初始化完成")
    
    def _set_default_config(self):
        """设置默认配置"""
        self.config = {
            'paths': {
                'data_dir': './data',
                'output_dir': './output',
                'temp_dir': './temp'
            },
            'md_analysis': {
                'n_processes': 8,
                'default_cutoff': 2.5,
                'timestep_fs': 10.0,
                'output_interval': 0.5
            },
            'plotting': {
                'figure_size_cm': [8, 6],
                'dpi': 300,
                'font_family': 'sans-serif',
                'font_name': 'Helvetica',
                'font_size': 12,
                'line_width': 1.0,
                'save_format': 'svg'
            },
            'file_processing': {
                'backup_files': True,
                'backup_suffix': '.bak',
                'encoding': 'utf-8'
            }
        }
        logger.info("📋 使用默认配置")
    
    def load_config(self, config_file: Optional[Union[str, Path]] = None):
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径，如果为None则使用初始化时的文件
        """
        if config_file:
            self.config_file = Path(config_file)
        
        if not self.config_file or not self.config_file.exists():
            logger.warning(f"⚠️ 配置文件不存在: {self.config_file}")
            self._set_default_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.suffix.lower() in ['.yaml', '.yml']:
                    if not YAML_AVAILABLE:
                        raise ImportError("需要安装PyYAML来读取YAML配置文件")
                    self.config = yaml.safe_load(f)
                elif self.config_file.suffix.lower() == '.json':
                    self.config = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {self.config_file.suffix}")
            
            logger.info(f"✅ 配置文件加载成功: {self.config_file}")
            
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            self._set_default_config()
    
    def save_config(self, config_file: Optional[Union[str, Path]] = None):
        """
        保存配置文件
        
        Args:
            config_file: 配置文件路径，如果为None则使用当前文件
        """
        if config_file:
            self.config_file = Path(config_file)
        
        if not self.config_file:
            logger.error("❌ 未指定配置文件路径")
            return
        
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.suffix.lower() in ['.yaml', '.yml']:
                    if not YAML_AVAILABLE:
                        raise ImportError("需要安装PyYAML来保存YAML配置文件")
                    yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                elif self.config_file.suffix.lower() == '.json':
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的配置文件格式: {self.config_file.suffix}")
            
            logger.info(f"💾 配置文件保存成功: {self.config_file}")
            
        except Exception as e:
            logger.error(f"❌ 配置文件保存失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值（支持点号分隔的嵌套键）
        
        Args:
            key: 配置键，支持 'section.subsection.key' 格式
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            logger.debug(f"配置键不存在: {key}，使用默认值: {default}")
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值（支持点号分隔的嵌套键）
        
        Args:
            key: 配置键，支持 'section.subsection.key' 格式
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        logger.debug(f"设置配置: {key} = {value}")
    
    def update(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新配置字典
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, new_config)
        logger.info("🔄 配置已更新")
    
    def get_paths(self) -> Dict[str, str]:
        """获取路径配置"""
        return self.get('paths', {})
    
    def get_md_config(self) -> Dict[str, Any]:
        """获取分子动力学分析配置"""
        return self.get('md_analysis', {})
    
    def get_plot_config(self) -> Dict[str, Any]:
        """获取绘图配置"""
        return self.get('plotting', {})
    
    def get_file_config(self) -> Dict[str, Any]:
        """获取文件处理配置"""
        return self.get('file_processing', {})
    
    def create_sample_config(self, output_file: Union[str, Path]):
        """
        创建示例配置文件
        
        Args:
            output_file: 输出文件路径
        """
        sample_config = {
            'paths': {
                'data_dir': './data',
                'output_dir': './output',
                'temp_dir': './temp'
            },
            'md_analysis': {
                'n_processes': 8,
                'default_cutoff': 2.5,
                'timestep_fs': 10.0,
                'output_interval': 0.5,
                'cell_parameters': [9.074, 10.975, 26.000, 90, 90, 90],
                'pbc': [True, True, True]
            },
            'plotting': {
                'figure_size_cm': [8, 6],
                'dpi': 300,
                'font_family': 'sans-serif',
                'font_name': 'Helvetica',
                'font_size': 12,
                'line_width': 1.0,
                'marker_size': 2.5,
                'save_format': 'svg',
                'colors': ['black', 'red', 'blue', 'green', 'orange']
            },
            'file_processing': {
                'backup_files': True,
                'backup_suffix': '.bak',
                'encoding': 'utf-8',
                'element_mappings': {
                    'Fe1': 'Fe',
                    'Fe2': 'Al'
                }
            },
            'analysis_parameters': {
                'cn_calculation': {
                    'cutoff_distance': 2.5,
                    'function_parameters': {
                        'rc': 3.0,
                        'nn': 12,
                        'mm': 24
                    }
                },
                'rdf_parameters': {
                    'r_max': 8.0,
                    'dr': 0.1
                }
            }
        }
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            if output_path.suffix.lower() in ['.yaml', '.yml']:
                if YAML_AVAILABLE:
                    yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True)
                else:
                    logger.error("❌ 需要安装PyYAML来创建YAML配置文件")
                    return
            else:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📝 示例配置文件已创建: {output_file}")
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return json.dumps(self.config, indent=2, ensure_ascii=False)
