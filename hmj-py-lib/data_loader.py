"""
数据加载模块 - 提供各种数据文件的加载和处理功能

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 统一的数据加载接口，支持多种格式的分子动力学数据文件
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Union, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataLoader:
    """数据加载器类 - 提供统一的数据加载接口"""
    
    @staticmethod
    def load_distance_data(filename: Union[str, Path], 
                          delimiter: str = None,
                          columns: Optional[List[str]] = None) -> np.ndarray:
        """
        加载距离数据文件
        
        Args:
            filename: 数据文件路径
            delimiter: 分隔符，默认自动检测
            columns: 列名列表
            
        Returns:
            np.ndarray: 距离数据数组
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 数据格式错误
        """
        try:
            filepath = Path(filename)
            if not filepath.exists():
                raise FileNotFoundError(f"文件不存在: {filename}")
            
            # 自动检测分隔符
            if delimiter is None:
                with open(filepath, 'r') as f:
                    first_line = f.readline().strip()
                    if '\t' in first_line:
                        delimiter = '\t'
                    elif ',' in first_line:
                        delimiter = ','
                    else:
                        delimiter = None  # 空白符分隔
            
            data = np.loadtxt(filepath, delimiter=delimiter)
            
            logger.info(f"✅ 成功加载数据文件: {filename}")
            logger.info(f"   数据形状: {data.shape}")
            if data.ndim == 1:
                logger.info(f"   数据范围: {data.min():.3f} - {data.max():.3f}")
            else:
                logger.info(f"   列数: {data.shape[1]}")
                
            return data
            
        except Exception as e:
            logger.error(f"❌ 加载文件失败: {filename} - {e}")
            raise
    
    @staticmethod
    def load_pmf_data(filename: Union[str, Path]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        加载PMF数据文件
        
        Args:
            filename: PMF数据文件路径
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (x, y, y_error)
        """
        try:
            data = DataLoader.load_distance_data(filename, delimiter='\t')
            
            # 假设PMF文件格式: 第0列为x，第6列为y，第7列为误差
            if data.shape[1] < 8:
                raise ValueError(f"PMF文件格式不正确，需要至少8列，实际{data.shape[1]}列")
            
            x = data[:, 0]
            y = data[:, 6] 
            y_error = data[:, 7]
            
            # 反转数据顺序（如果需要）
            return x[::-1], y[::-1], y_error[::-1]
            
        except Exception as e:
            logger.error(f"❌ 加载PMF数据失败: {filename} - {e}")
            raise
    
    @staticmethod
    def load_rdf_data(filename: Union[str, Path]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        加载RDF数据文件
        
        Args:
            filename: RDF数据文件路径
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (distance, rdf, cn)
        """
        try:
            data = DataLoader.load_distance_data(filename)
            
            if data.shape[1] < 3:
                raise ValueError(f"RDF文件格式不正确，需要至少3列，实际{data.shape[1]}列")
            
            distance = data[:, 0]
            rdf = data[:, 1]
            cn = data[:, 2]
            
            logger.info(f"✅ 成功加载RDF数据: {filename}")
            logger.info(f"   距离范围: {distance.min():.3f} - {distance.max():.3f} Å")
            logger.info(f"   RDF范围: {rdf.min():.3f} - {rdf.max():.3f}")
            logger.info(f"   CN范围: {cn.min():.3f} - {cn.max():.3f}")
            
            return distance, rdf, cn
            
        except Exception as e:
            logger.error(f"❌ 加载RDF数据失败: {filename} - {e}")
            raise
    
    @staticmethod
    def create_time_axis(data_length: int, 
                        timestep_fs: float = 10.0, 
                        output_interval: float = 0.5) -> np.ndarray:
        """
        创建时间轴
        
        Args:
            data_length: 数据点数量
            timestep_fs: 时间步长 (飞秒)
            output_interval: 输出间隔 (以时间步为单位)
            
        Returns:
            np.ndarray: 时间轴 (皮秒)
        """
        # 计算时间轴: 数据点索引 × 时间步长 × 输出间隔 / 1000 (fs -> ps)
        time_ps = np.arange(data_length) * timestep_fs * output_interval / 1000
        
        logger.info(f"📊 时间轴信息:")
        logger.info(f"   时间范围: 0 - {time_ps[-1]:.2f} ps")
        logger.info(f"   时间步长: {timestep_fs} fs")
        logger.info(f"   输出间隔: {output_interval}")
        
        return time_ps
    
    @staticmethod
    def validate_data(data: np.ndarray, 
                     expected_shape: Optional[Tuple] = None,
                     check_finite: bool = True) -> bool:
        """
        验证数据有效性
        
        Args:
            data: 要验证的数据
            expected_shape: 期望的数据形状
            check_finite: 是否检查有限值
            
        Returns:
            bool: 数据是否有效
        """
        try:
            if data is None or data.size == 0:
                logger.warning("⚠️ 数据为空")
                return False
            
            if expected_shape and data.shape != expected_shape:
                logger.warning(f"⚠️ 数据形状不匹配: 期望{expected_shape}, 实际{data.shape}")
                return False
            
            if check_finite and not np.all(np.isfinite(data)):
                logger.warning("⚠️ 数据包含无限值或NaN")
                return False
            
            logger.info("✅ 数据验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据验证失败: {e}")
            return False
