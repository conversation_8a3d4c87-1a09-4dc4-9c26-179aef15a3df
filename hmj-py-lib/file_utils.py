"""
文件工具模块 - 提供文件处理和转换功能

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 文件操作工具，包括坐标转换、批处理等功能
"""

import os
import shutil
from pathlib import Path
from typing import Union, List, Dict, Optional, Callable
import logging
import re

logger = logging.getLogger(__name__)


class FileUtils:
    """文件工具类 - 提供各种文件操作功能"""
    
    @staticmethod
    def find_files(directory: Union[str, Path], 
                   pattern: str = "*",
                   recursive: bool = False) -> List[Path]:
        """
        查找文件
        
        Args:
            directory: 搜索目录
            pattern: 文件模式（支持通配符）
            recursive: 是否递归搜索
            
        Returns:
            List[Path]: 找到的文件列表
        """
        directory = Path(directory)
        
        if not directory.exists():
            logger.warning(f"⚠️ 目录不存在: {directory}")
            return []
        
        if recursive:
            files = list(directory.rglob(pattern))
        else:
            files = list(directory.glob(pattern))
        
        # 只返回文件，不包括目录
        files = [f for f in files if f.is_file()]
        
        logger.info(f"🔍 在 {directory} 中找到 {len(files)} 个文件 (模式: {pattern})")
        
        return files
    
    @staticmethod
    def backup_file(filepath: Union[str, Path], 
                   backup_suffix: str = ".bak") -> Path:
        """
        备份文件
        
        Args:
            filepath: 要备份的文件路径
            backup_suffix: 备份文件后缀
            
        Returns:
            Path: 备份文件路径
        """
        filepath = Path(filepath)
        backup_path = filepath.with_suffix(filepath.suffix + backup_suffix)
        
        if filepath.exists():
            shutil.copy2(filepath, backup_path)
            logger.info(f"💾 文件已备份: {filepath} -> {backup_path}")
        else:
            logger.warning(f"⚠️ 源文件不存在: {filepath}")
        
        return backup_path
    
    @staticmethod
    def batch_process_files(file_list: List[Path],
                           processor: Callable[[Path], None],
                           backup: bool = True,
                           dry_run: bool = False) -> Dict[str, int]:
        """
        批量处理文件
        
        Args:
            file_list: 要处理的文件列表
            processor: 处理函数
            backup: 是否备份原文件
            dry_run: 是否只是预演（不实际执行）
            
        Returns:
            Dict[str, int]: 处理结果统计
        """
        results = {"success": 0, "failed": 0, "skipped": 0}
        
        logger.info(f"🚀 开始批量处理 {len(file_list)} 个文件")
        if dry_run:
            logger.info("🔍 预演模式 - 不会实际修改文件")
        
        for i, filepath in enumerate(file_list, 1):
            try:
                logger.info(f"📁 处理文件 {i}/{len(file_list)}: {filepath.name}")
                
                if not filepath.exists():
                    logger.warning(f"⚠️ 文件不存在，跳过: {filepath}")
                    results["skipped"] += 1
                    continue
                
                if dry_run:
                    logger.info(f"🔍 [预演] 将处理: {filepath}")
                    results["success"] += 1
                    continue
                
                # 备份原文件
                if backup:
                    FileUtils.backup_file(filepath)
                
                # 执行处理
                processor(filepath)
                results["success"] += 1
                logger.info(f"✅ 处理成功: {filepath.name}")
                
            except Exception as e:
                logger.error(f"❌ 处理失败: {filepath.name} - {e}")
                results["failed"] += 1
        
        logger.info(f"📊 批量处理完成: 成功 {results['success']}, 失败 {results['failed']}, 跳过 {results['skipped']}")
        
        return results
    
    @staticmethod
    def replace_in_file(filepath: Union[str, Path],
                       replacements: Dict[str, str],
                       backup: bool = True) -> bool:
        """
        在文件中进行文本替换
        
        Args:
            filepath: 文件路径
            replacements: 替换字典 {old_text: new_text}
            backup: 是否备份原文件
            
        Returns:
            bool: 是否成功
        """
        try:
            filepath = Path(filepath)
            
            if not filepath.exists():
                logger.error(f"❌ 文件不存在: {filepath}")
                return False
            
            # 备份原文件
            if backup:
                FileUtils.backup_file(filepath)
            
            # 读取文件内容
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 执行替换
            modified = False
            for old_text, new_text in replacements.items():
                if old_text in content:
                    content = content.replace(old_text, new_text)
                    modified = True
                    logger.info(f"🔄 替换: '{old_text}' -> '{new_text}'")
            
            # 写回文件
            if modified:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"✅ 文件已更新: {filepath}")
            else:
                logger.info(f"ℹ️ 文件无需修改: {filepath}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 文件替换失败: {filepath} - {e}")
            return False
    
    @staticmethod
    def convert_xyz_elements(filepath: Union[str, Path],
                           element_map: Dict[str, str],
                           output_suffix: str = "_converted") -> Optional[Path]:
        """
        转换XYZ文件中的元素符号
        
        Args:
            filepath: XYZ文件路径
            element_map: 元素映射字典 {old_element: new_element}
            output_suffix: 输出文件后缀
            
        Returns:
            Optional[Path]: 输出文件路径，失败时返回None
        """
        try:
            filepath = Path(filepath)
            output_path = filepath.with_stem(filepath.stem + output_suffix)
            
            if not filepath.exists():
                logger.error(f"❌ 文件不存在: {filepath}")
                return None
            
            with open(filepath, 'r') as infile, open(output_path, 'w') as outfile:
                lines = infile.readlines()
                
                for line in lines:
                    modified_line = line
                    for old_element, new_element in element_map.items():
                        # 使用正则表达式确保只替换完整的元素符号
                        pattern = r'\b' + re.escape(old_element) + r'\b'
                        modified_line = re.sub(pattern, new_element, modified_line)
                    
                    outfile.write(modified_line)
            
            logger.info(f"✅ XYZ转换完成: {filepath} -> {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ XYZ转换失败: {filepath} - {e}")
            return None
    
    @staticmethod
    def rename_files(directory: Union[str, Path],
                    pattern: str,
                    replacement: str,
                    dry_run: bool = False) -> Dict[str, int]:
        """
        批量重命名文件
        
        Args:
            directory: 目录路径
            pattern: 要替换的模式（正则表达式）
            replacement: 替换文本
            dry_run: 是否只是预演
            
        Returns:
            Dict[str, int]: 重命名结果统计
        """
        directory = Path(directory)
        results = {"renamed": 0, "skipped": 0, "failed": 0}
        
        if not directory.exists():
            logger.error(f"❌ 目录不存在: {directory}")
            return results
        
        logger.info(f"🔄 开始批量重命名文件 (模式: {pattern} -> {replacement})")
        if dry_run:
            logger.info("🔍 预演模式 - 不会实际重命名文件")
        
        for filepath in directory.iterdir():
            if not filepath.is_file():
                continue
            
            try:
                old_name = filepath.name
                new_name = re.sub(pattern, replacement, old_name)
                
                if old_name == new_name:
                    results["skipped"] += 1
                    continue
                
                new_path = filepath.parent / new_name
                
                if dry_run:
                    logger.info(f"🔍 [预演] 重命名: {old_name} -> {new_name}")
                    results["renamed"] += 1
                else:
                    if new_path.exists():
                        logger.warning(f"⚠️ 目标文件已存在，跳过: {new_name}")
                        results["skipped"] += 1
                        continue
                    
                    filepath.rename(new_path)
                    logger.info(f"✅ 重命名: {old_name} -> {new_name}")
                    results["renamed"] += 1
                
            except Exception as e:
                logger.error(f"❌ 重命名失败: {filepath.name} - {e}")
                results["failed"] += 1
        
        logger.info(f"📊 重命名完成: 重命名 {results['renamed']}, 跳过 {results['skipped']}, 失败 {results['failed']}")
        
        return results
