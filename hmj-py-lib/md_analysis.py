"""
分子动力学分析模块 - 提供MD轨迹分析功能

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 分子动力学轨迹分析工具，包括配位数计算、距离分析等
"""

import numpy as np
from typing import List, Tuple, Optional, Union, Callable
from functools import partial
from multiprocessing import Pool
import logging

try:
    import ase
    from ase import io
    ASE_AVAILABLE = True
except ImportError:
    ASE_AVAILABLE = False
    logging.warning("ASE未安装，某些功能将不可用")

logger = logging.getLogger(__name__)


class MDAnalyzer:
    """分子动力学分析器类"""
    
    def __init__(self, n_processes: int = 8):
        """
        初始化MD分析器
        
        Args:
            n_processes: 并行处理进程数
        """
        self.n_processes = n_processes
        logger.info(f"🔬 MD分析器初始化完成 (进程数: {n_processes})")
    
    @staticmethod
    def split_lammps_trajectory(trajectory_file: str) -> List[List]:
        """
        分割LAMMPS轨迹文件
        
        Args:
            trajectory_file: LAMMPS轨迹文件路径
            
        Returns:
            List[List]: 轨迹帧列表
        """
        logger.info(f"📂 正在读取轨迹文件: {trajectory_file}")
        
        try:
            with open(trajectory_file, 'r') as f:
                data_file = f.read().split('ITEM: TIMESTEP\n')
            
            logger.info("🔄 正在分割轨迹...")
            trajectory = []
            
            for iframe, frame in enumerate(data_file[1:]):
                if iframe % 500 == 0:
                    logger.info(f"   处理帧: {iframe}")
                
                lines = frame.split('\n')[8:-1]  # 跳过头部信息
                config = []
                
                for line in lines:
                    if line.strip():  # 跳过空行
                        parts = line.split()
                        if len(parts) >= 7:
                            atom_id, element, charge, mass, xu, yu, zu = parts[:7]
                            position = np.array([float(xu), float(yu), float(zu)])
                            config.append([int(atom_id), element, float(charge), float(mass), position])
                
                trajectory.append(config)
            
            logger.info(f"✅ 轨迹分割完成: {len(trajectory)} 帧")
            return trajectory
            
        except Exception as e:
            logger.error(f"❌ 轨迹文件读取失败: {e}")
            raise
    
    @staticmethod
    def split_xyz_trajectory(xyz_file: str) -> List[List]:
        """
        分割XYZ轨迹文件
        
        Args:
            xyz_file: XYZ轨迹文件路径
            
        Returns:
            List[List]: 轨迹帧列表
        """
        if not ASE_AVAILABLE:
            raise ImportError("需要安装ASE库来处理XYZ文件")
        
        logger.info(f"📂 正在读取XYZ文件: {xyz_file}")
        
        try:
            xyz_frames = io.read(xyz_file, ':')
            trajectory = []
            
            for frame in xyz_frames:
                config = []
                for atom in frame:
                    config.append([
                        atom.index, 
                        atom.symbol, 
                        0,  # 电荷（XYZ文件中通常没有）
                        atom.mass, 
                        np.array([atom.x, atom.y, atom.z])
                    ])
                trajectory.append(config)
            
            logger.info(f"✅ XYZ轨迹读取完成: {len(trajectory)} 帧")
            return trajectory
            
        except Exception as e:
            logger.error(f"❌ XYZ文件读取失败: {e}")
            raise
    
    @staticmethod
    def trajectory_to_ase_atoms(trajectory: List[List], 
                               cell: List[float], 
                               pbc: Tuple[bool, bool, bool] = (True, True, True)) -> List:
        """
        将轨迹转换为ASE Atoms对象
        
        Args:
            trajectory: 轨迹数据
            cell: 晶胞参数 [a, b, c, alpha, beta, gamma]
            pbc: 周期性边界条件
            
        Returns:
            List: ASE Atoms对象列表
        """
        if not ASE_AVAILABLE:
            raise ImportError("需要安装ASE库")
        
        logger.info("🔄 正在转换轨迹为ASE Atoms对象...")
        
        ase_trajectory = []
        for frame in trajectory:
            frame_array = np.array(frame, dtype=object)
            positions = np.array([pos for pos in frame_array[:, 4]])
            symbols = frame_array[:, 1]
            charges = frame_array[:, 2].astype(float)
            masses = frame_array[:, 3].astype(float)
            
            atoms = ase.Atoms(
                symbols=symbols,
                cell=cell,
                pbc=pbc,
                positions=positions,
                charges=charges,
                masses=masses
            )
            ase_trajectory.append(atoms)
        
        logger.info(f"✅ 转换完成: {len(ase_trajectory)} 帧")
        return ase_trajectory
    
    @staticmethod
    def coordination_function(r: float, 
                            rc: float = 2.75, 
                            nn: int = 12, 
                            mm: int = 24) -> float:
        """
        配位数计算函数
        
        Args:
            r: 距离
            rc: 截断距离
            nn: 参数n
            mm: 参数m
            
        Returns:
            float: 配位数贡献
        """
        try:
            return (1 - (r / rc) ** nn) / (1 - (r / rc) ** mm)
        except ZeroDivisionError:
            return 0.5
    
    @staticmethod
    def fermi_function(r: float, 
                      rc: float = 1.35, 
                      kappa: float = 0.1) -> float:
        """
        Fermi函数
        
        Args:
            r: 距离
            rc: 截断距离
            kappa: 参数kappa
            
        Returns:
            float: Fermi函数值
        """
        return 1.0 / (np.exp((r - rc) / kappa) + 1)
    
    def calculate_cn_single_frame(self, 
                                 iframe: int,
                                 trajectory: List,
                                 center_atoms: List[int],
                                 pair_atoms: List[int],
                                 cutoff: float = 2.5,
                                 use_function: bool = False,
                                 cn_function: Optional[Callable] = None,
                                 **function_kwargs) -> float:
        """
        计算单帧的配位数
        
        Args:
            iframe: 帧索引
            trajectory: ASE轨迹
            center_atoms: 中心原子索引列表
            pair_atoms: 配对原子索引列表
            cutoff: 截断距离
            use_function: 是否使用函数计算
            cn_function: 配位数函数
            **function_kwargs: 函数参数
            
        Returns:
            float: 平均配位数
        """
        if iframe % 100 == 0:
            logger.info(f"   计算帧: {iframe}")
        
        cn_total = 0.0
        
        for center_idx in center_atoms:
            for pair_idx in pair_atoms:
                if center_idx != pair_idx:  # 避免自相互作用
                    distance = trajectory[iframe].get_distance(center_idx, pair_idx, mic=True)
                    
                    if use_function and cn_function:
                        cn_total += cn_function(distance, **function_kwargs)
                    elif distance <= cutoff:
                        cn_total += 1.0
        
        return cn_total / len(center_atoms)
    
    def calculate_cn_trajectory(self,
                               trajectory: List,
                               center_atoms: List[int],
                               pair_atoms: List[int],
                               cutoff: float = 2.5,
                               use_function: bool = False,
                               cn_function: Optional[Callable] = None,
                               **function_kwargs) -> List[float]:
        """
        计算整个轨迹的配位数
        
        Args:
            trajectory: ASE轨迹
            center_atoms: 中心原子索引列表
            pair_atoms: 配对原子索引列表
            cutoff: 截断距离
            use_function: 是否使用函数计算
            cn_function: 配位数函数
            **function_kwargs: 函数参数
            
        Returns:
            List[float]: 每帧的配位数
        """
        logger.info(f"🧮 开始计算配位数 (中心原子: {len(center_atoms)}, 配对原子: {len(pair_atoms)})")
        
        # 创建部分函数用于并行计算
        calc_func = partial(
            self.calculate_cn_single_frame,
            trajectory=trajectory,
            center_atoms=center_atoms,
            pair_atoms=pair_atoms,
            cutoff=cutoff,
            use_function=use_function,
            cn_function=cn_function,
            **function_kwargs
        )
        
        frame_indices = range(len(trajectory))
        
        with Pool(self.n_processes) as pool:
            cn_values = pool.map(calc_func, frame_indices)
        
        logger.info(f"✅ 配位数计算完成: {len(cn_values)} 帧")
        logger.info(f"   配位数范围: {min(cn_values):.3f} - {max(cn_values):.3f}")
        
        return cn_values
    
    @staticmethod
    def get_atom_indices_by_symbol(trajectory_frame: List, 
                                  symbol: str) -> List[int]:
        """
        根据元素符号获取原子索引
        
        Args:
            trajectory_frame: 轨迹帧
            symbol: 元素符号
            
        Returns:
            List[int]: 原子索引列表
        """
        if ASE_AVAILABLE and hasattr(trajectory_frame, 'get_chemical_symbols'):
            # ASE Atoms对象
            indices = [i for i, s in enumerate(trajectory_frame.get_chemical_symbols()) if s == symbol]
        else:
            # 原始轨迹数据
            indices = [atom[0] for atom in trajectory_frame if atom[1] == symbol]
        
        logger.info(f"🔍 找到 {len(indices)} 个 {symbol} 原子")
        return indices
