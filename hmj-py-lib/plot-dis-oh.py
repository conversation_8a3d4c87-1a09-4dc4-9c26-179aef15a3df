import numpy as np
import matplotlib.pyplot as plt

def plot_data(file1, file2, save_path, x_range=(0, 8), figure_size=(8, 6)):
    # Load data
    F = np.loadtxt(file1)
    E = np.loadtxt(file2)

    # 创建指定大小的图
    plt.figure(figsize=(figure_size[0]/2.54, figure_size[1]/2.54))
    plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
    plt.rcParams['svg.fonttype'] = 'none'

    # 绘制图形
    plt.plot(E[:, 0] * 10 * 0.5 / 1000, E[:, 1], 'r', linewidth=1, label='E Data')
    plt.plot(F[:, 0] * 10 * 0.5 / 1000, F[:, 1], 'k', linewidth=1, label='F Data')

    # 设置坐标轴标签
    Ang = 'Å'
    plt.xlabel('Time (ps)')
    plt.ylabel(f'Distance ({Ang})')

    # 设置 x 轴范围和刻度
    plt.xlim(x_range)
    plt.xticks(np.arange(x_range[0], x_range[1] + 1, 2))

    # 保存图形
    plt.savefig(save_path, bbox_inches='tight', format='svg')

    # 显示图形
    plt.show()


# # 假设你的库名为 mylibrary
# import mylibrary

# # 调用 plot_data 函数，并指定图的大小为 (10, 8)
# mylibrary.plot_data('file1.dat', 'file2.dat', 'output.svg', figure_size=(10, 8))
