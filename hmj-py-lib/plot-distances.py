"""
距离分析绘图脚本 - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2023-10-26 13:20:58
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 16:30:00
Description: 分析和可视化原子间距离随时间的变化

Copyright (c) 2023 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path

# 添加工具库路径
sys.path.append(str(Path(__file__).parent))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def plot_distance_analysis(data_file: str = 'Mo-O.dat',
                          output_file: str = 'distance_analysis.svg',
                          config_file: str = None):
    """
    执行距离分析和绘图

    Args:
        data_file: 距离数据文件
        output_file: 输出图形文件
        config_file: 配置文件路径
    """
    try:
        # 初始化配置管理器
        config = ConfigManager(config_file)

        # 初始化数据加载器和绘图管理器
        data_loader = DataLoader()
        plot_manager = PlotManager(config.get_plot_config())

        logger.info("🚀 开始距离分析...")

        # 加载距离数据
        distance_data = data_loader.load_distance_data(data_file)

        # 创建时间轴
        md_config = config.get_md_config()
        time_data = data_loader.create_time_axis(
            len(distance_data),
            timestep_fs=md_config.get('timestep_fs', 10.0),
            output_interval=md_config.get('output_interval', 0.5)
        )

        # 绘制图形
        fig = plot_manager.plot_distance_vs_time(
            time_data, distance_data,
            xlabel='Time (ps)',
            ylabel='Distance (Å)',
            title='Distance vs Time Analysis'
        )

        # 保存和显示
        plot_manager.show_and_save(fig, output_file, show=True)

        logger.info("✅ 距离分析完成!")

    except Exception as e:
        logger.error(f"❌ 距离分析失败: {e}")
        raise


if __name__ == "__main__":
    # 可以通过命令行参数自定义文件名
    import argparse

    parser = argparse.ArgumentParser(description='距离分析绘图工具')
    parser.add_argument('--data', '-d', default='Mo-O.dat', help='距离数据文件')
    parser.add_argument('--output', '-o', default='distance_analysis.svg', help='输出图形文件')
    parser.add_argument('--config', '-c', default=None, help='配置文件路径')

    args = parser.parse_args()

    plot_distance_analysis(args.data, args.output, args.config)
