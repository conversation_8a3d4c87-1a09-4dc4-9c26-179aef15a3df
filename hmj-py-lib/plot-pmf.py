"""
PMF绘图脚本 - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2023-10-26 13:20:58
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 16:30:00
Description: 绘制势能面(PMF)图形

Copyright (c) 2023 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path

# 添加工具库路径
sys.path.append(str(Path(__file__).parent))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def plot_pmf_analysis(data_file: str = 'PMF.dat',
                     output_file: str = 'PMF_plot.svg',
                     config_file: str = None,
                     x_range: tuple = (1, 0.1),
                     x_tick_interval: float = 0.2,
                     invert_x: bool = True):
    """
    执行PMF分析和绘图

    Args:
        data_file: PMF数据文件
        output_file: 输出图形文件
        config_file: 配置文件路径
        x_range: x轴范围
        x_tick_interval: x轴刻度间隔
        invert_x: 是否反转x轴
    """
    try:
        # 初始化配置管理器
        config = ConfigManager(config_file)

        # 初始化数据加载器和绘图管理器
        data_loader = DataLoader()
        plot_manager = PlotManager(config.get_plot_config())

        logger.info("🚀 开始PMF分析...")

        # 加载PMF数据
        x_data, y_data, y_error = data_loader.load_pmf_data(data_file)

        # 绘制PMF图形
        fig = plot_manager.plot_pmf(
            x_data, y_data, y_error,
            xlabel='CN',
            ylabel='Free energy (kcal/mol)',
            x_range=x_range,
            x_tick_interval=x_tick_interval,
            invert_x=invert_x
        )

        # 保存和显示
        plot_manager.show_and_save(fig, output_file, show=True)

        logger.info("✅ PMF分析完成!")

    except Exception as e:
        logger.error(f"❌ PMF分析失败: {e}")
        raise


if __name__ == "__main__":
    # 可以通过命令行参数自定义文件名
    import argparse

    parser = argparse.ArgumentParser(description='PMF绘图工具')
    parser.add_argument('--data', '-d', default='PMF.dat', help='PMF数据文件')
    parser.add_argument('--output', '-o', default='PMF_plot.svg', help='输出图形文件')
    parser.add_argument('--config', '-c', default=None, help='配置文件路径')
    parser.add_argument('--x-range', nargs=2, type=float, default=[1, 0.1], help='x轴范围')
    parser.add_argument('--x-interval', type=float, default=0.2, help='x轴刻度间隔')
    parser.add_argument('--no-invert-x', action='store_true', help='不反转x轴')

    args = parser.parse_args()

    plot_pmf_analysis(
        args.data,
        args.output,
        args.config,
        tuple(args.x_range),
        args.x_interval,
        not args.no_invert_x
    )
