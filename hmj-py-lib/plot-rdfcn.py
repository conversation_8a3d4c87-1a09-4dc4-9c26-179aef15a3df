"""
RDF和CN绘图脚本 - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2023-10-26 13:20:58
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 16:30:00
Description: 绘制径向分布函数(RDF)和配位数(CN)图形

Copyright (c) 2023 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path

# 添加工具库路径
sys.path.append(str(Path(__file__).parent))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def plot_rdf_cn_analysis(data_file: str = 'Od1_rdfcn.dat',
                        output_file: str = 'Od1_rdfcn.svg',
                        config_file: str = None,
                        x_range: tuple = (2, 4.7),
                        rdf_ylim: tuple = (0, 3),
                        cn_ylim: tuple = (0, 4)):
    """
    执行RDF和CN分析和绘图

    Args:
        data_file: RDF/CN数据文件
        output_file: 输出图形文件
        config_file: 配置文件路径
        x_range: x轴范围
        rdf_ylim: RDF y轴范围
        cn_ylim: CN y轴范围
    """
    try:
        # 初始化配置管理器
        config = ConfigManager(config_file)

        # 初始化数据加载器和绘图管理器
        data_loader = DataLoader()
        plot_manager = PlotManager(config.get_plot_config())

        logger.info("🚀 开始RDF/CN分析...")

        # 加载RDF/CN数据
        distance, rdf, cn = data_loader.load_rdf_data(data_file)

        # 绘制RDF/CN图形
        fig = plot_manager.plot_rdf_cn(
            distance, rdf, cn,
            x_range=x_range,
            rdf_ylim=rdf_ylim,
            cn_ylim=cn_ylim
        )

        # 保存和显示
        plot_manager.show_and_save(fig, output_file, show=True)

        logger.info("✅ RDF/CN分析完成!")

    except Exception as e:
        logger.error(f"❌ RDF/CN分析失败: {e}")
        raise


if __name__ == "__main__":
    # 可以通过命令行参数自定义文件名
    import argparse

    parser = argparse.ArgumentParser(description='RDF/CN绘图工具')
    parser.add_argument('--data', '-d', default='Od1_rdfcn.dat', help='RDF/CN数据文件')
    parser.add_argument('--output', '-o', default='Od1_rdfcn.svg', help='输出图形文件')
    parser.add_argument('--config', '-c', default=None, help='配置文件路径')
    parser.add_argument('--x-range', nargs=2, type=float, default=[2, 4.7], help='x轴范围')
    parser.add_argument('--rdf-ylim', nargs=2, type=float, default=[0, 3], help='RDF y轴范围')
    parser.add_argument('--cn-ylim', nargs=2, type=float, default=[0, 4], help='CN y轴范围')

    args = parser.parse_args()

    plot_rdf_cn_analysis(
        args.data,
        args.output,
        args.config,
        tuple(args.x_range),
        tuple(args.rdf_ylim),
        tuple(args.cn_ylim)
    )
