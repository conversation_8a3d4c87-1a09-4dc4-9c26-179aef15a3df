"""
绘图管理模块 - 提供统一的绘图接口和样式管理

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 统一的绘图管理器，提供一致的绘图样式和常用绘图功能
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
from pathlib import Path
from typing import Union, Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class PlotManager:
    """绘图管理器类 - 提供统一的绘图接口"""
    
    # 默认绘图配置
    DEFAULT_CONFIG = {
        'figure_size_cm': (8, 6),  # 图形大小(厘米)
        'dpi': 300,
        'font_family': 'sans-serif',
        'font_name': 'Helvetica',
        'font_size': 12,
        'line_width': 1.0,
        'marker_size': 2.5,
        'colors': ['black', 'red', 'blue', 'green', 'orange'],
        'line_styles': ['-', '--', '-.', ':'],
        'save_format': 'svg'
    }
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化绘图管理器
        
        Args:
            config: 自定义配置字典
        """
        self.config = self.DEFAULT_CONFIG.copy()
        if config:
            self.config.update(config)
        
        self._setup_matplotlib()
    
    def _setup_matplotlib(self):
        """设置matplotlib全局参数"""
        plt.rcParams.update({
            'font.size': self.config['font_size'],
            'font.family': self.config['font_family'],
            'font.sans-serif': self.config['font_name'],
            'svg.fonttype': 'none',  # 确保SVG中的文字可编辑
            'figure.dpi': self.config['dpi']
        })
        
        logger.info("🎨 绘图样式设置完成")
    
    def create_figure(self, size_cm: Optional[Tuple[float, float]] = None) -> plt.Figure:
        """
        创建图形
        
        Args:
            size_cm: 图形大小(厘米)，默认使用配置中的大小
            
        Returns:
            plt.Figure: matplotlib图形对象
        """
        if size_cm is None:
            size_cm = self.config['figure_size_cm']
        
        # 转换厘米到英寸
        size_inch = (size_cm[0] / 2.54, size_cm[1] / 2.54)
        
        fig = plt.figure(figsize=size_inch)
        logger.info(f"📊 创建图形: {size_cm[0]}×{size_cm[1]} cm")
        
        return fig
    
    def plot_distance_vs_time(self, time_data: np.ndarray, 
                             distance_data: np.ndarray,
                             xlabel: str = 'Time (ps)',
                             ylabel: str = 'Distance (Å)',
                             title: str = '',
                             line_color: str = 'black',
                             line_width: Optional[float] = None,
                             show_legend: bool = False,
                             label: str = '') -> plt.Figure:
        """
        绘制距离随时间变化的图形
        
        Args:
            time_data: 时间数据
            distance_data: 距离数据
            xlabel: x轴标签
            ylabel: y轴标签
            title: 图形标题
            line_color: 线条颜色
            line_width: 线条宽度
            show_legend: 是否显示图例
            label: 数据标签
            
        Returns:
            plt.Figure: 图形对象
        """
        fig = self.create_figure()
        
        if line_width is None:
            line_width = self.config['line_width']
        
        plt.plot(time_data, distance_data,
                color=line_color,
                linewidth=line_width,
                label=label if show_legend else None)
        
        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        
        if title:
            plt.title(title)
        
        if show_legend and label:
            plt.legend()
        
        logger.info(f"📈 绘制距离-时间图: {len(time_data)} 个数据点")
        
        return fig
    
    def plot_pmf(self, x_data: np.ndarray, 
                 y_data: np.ndarray, 
                 y_error: np.ndarray,
                 xlabel: str = 'CN',
                 ylabel: str = 'Free energy (kcal/mol)',
                 x_range: Optional[Tuple[float, float]] = None,
                 x_tick_interval: Optional[float] = None,
                 invert_x: bool = False) -> plt.Figure:
        """
        绘制PMF图形
        
        Args:
            x_data: x轴数据
            y_data: y轴数据
            y_error: 误差数据
            xlabel: x轴标签
            ylabel: y轴标签
            x_range: x轴范围
            x_tick_interval: x轴刻度间隔
            invert_x: 是否反转x轴
            
        Returns:
            plt.Figure: 图形对象
        """
        fig = self.create_figure()
        
        plt.errorbar(x_data, y_data, yerr=y_error,
                    fmt='o-',
                    linewidth=self.config['line_width'],
                    markersize=self.config['marker_size'],
                    capsize=2,
                    elinewidth=0.5,
                    color='black')
        
        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        
        if x_range:
            plt.xlim(x_range)
            if x_tick_interval:
                if invert_x:
                    x_ticks = np.arange(x_range[0], x_range[1] - x_tick_interval, -x_tick_interval)
                else:
                    x_ticks = np.arange(x_range[0], x_range[1] + x_tick_interval, x_tick_interval)
                plt.xticks(x_ticks)
        
        if invert_x:
            plt.gca().invert_xaxis()
        
        logger.info(f"📊 绘制PMF图: {len(x_data)} 个数据点")
        
        return fig
    
    def plot_rdf_cn(self, distance: np.ndarray, 
                    rdf: np.ndarray, 
                    cn: np.ndarray,
                    x_range: Optional[Tuple[float, float]] = None,
                    rdf_ylim: Tuple[float, float] = (0, 3),
                    cn_ylim: Tuple[float, float] = (0, 4)) -> plt.Figure:
        """
        绘制RDF和CN双y轴图形
        
        Args:
            distance: 距离数据
            rdf: RDF数据
            cn: CN数据
            x_range: x轴范围
            rdf_ylim: RDF y轴范围
            cn_ylim: CN y轴范围
            
        Returns:
            plt.Figure: 图形对象
        """
        fig = self.create_figure()
        
        # 创建主轴
        ax1 = plt.gca()
        ax1.set_ylim(rdf_ylim)
        ax1.plot(distance, rdf, '-k', linewidth=self.config['line_width'], label='RDF')
        ax1.set_ylabel('RDF')
        ax1.set_xlabel('Distance (Å)')
        ax1.tick_params(axis='both', colors='black')
        
        # 创建次轴
        ax2 = ax1.twinx()
        ax2.set_ylim(cn_ylim)
        ax2.plot(distance, cn, '--k', linewidth=self.config['line_width'], label='CN')
        ax2.set_ylabel('CN')
        
        if x_range:
            plt.xlim(x_range)
        
        logger.info(f"📊 绘制RDF-CN图: {len(distance)} 个数据点")
        
        return fig
    
    def save_figure(self, fig: plt.Figure, 
                   filename: Union[str, Path],
                   format: Optional[str] = None,
                   dpi: Optional[int] = None,
                   bbox_inches: str = 'tight') -> None:
        """
        保存图形
        
        Args:
            fig: 图形对象
            filename: 文件名
            format: 保存格式
            dpi: 分辨率
            bbox_inches: 边界框设置
        """
        if format is None:
            format = self.config['save_format']
        
        if dpi is None:
            dpi = self.config['dpi']
        
        fig.savefig(filename, bbox_inches=bbox_inches, format=format, dpi=dpi)
        logger.info(f"💾 图形已保存: {filename}")
    
    def show_and_save(self, fig: plt.Figure, 
                     filename: Union[str, Path],
                     show: bool = True) -> None:
        """
        显示并保存图形
        
        Args:
            fig: 图形对象
            filename: 保存文件名
            show: 是否显示图形
        """
        self.save_figure(fig, filename)
        
        if show:
            plt.show()
            logger.info("👁️ 图形已显示")
        
        plt.close(fig)  # 关闭图形释放内存
