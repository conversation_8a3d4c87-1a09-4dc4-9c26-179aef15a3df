#coding=utf-8
import numpy as np
import matplotlib.pyplot as plt
import re
import os
########################################################################
au2ev = 27.211
# equ1, equ2, equ3 分别是en1 en05 en0 丢掉的数据数
equ1 = 4000; equ2 = 10000; equ3 = 6000

##### 累计平均值
def accum_ave(a):
    b = []
    b.append(a[0])
    for i in range(1, len(a)):
        b.append((b[i - 1] * i + a[i]) / float((i + 1)))
    return b

##### three point Simpson
def dpa(list):
    return round((list[0] + list[2]) / 6.0 + 2 * list[1] / 3.0, 2)

en1 = np.loadtxt('C:\\Users\\<USER>\\desktop\\en1')*au2ev
en05 = np.loadtxt('C:\\Users\\<USER>\\desktop\\en05')*au2ev
en0 = np.loadtxt('C:\\Users\\<USER>\\desktop\\en0')*au2ev
a1 = en1[equ1:]; a05 = en05[equ2:]; a0 = en0[equ3:]
b1 = accum_ave(a1);b05 = accum_ave(a05);b0 = accum_ave(a0)
ave1 = round(np.mean(a1),2);ave05 = round(np.mean(a05),2);ave0 = round(np.mean(a0),2)
ave = [ave1,ave05,ave0]; dpa_energy = dpa(ave)
ave.append(dpa_energy)

####统计误差
def error(c):
    n=len(c)
    half1 = c[0:int(n/2)]
    half2 = c[int(n/2):]
    return abs((np.mean(half1) - np.mean(half2))/2)
 
error1 = round(error(a1),2);error05 = round(error(a05),2);error0 = round(error(a0),2)
error = [error1,error05,error0];dpa_error = dpa(error)
error.append(dpa_error)

####输出三点积分自由能和误差
print(ave)
print(error)

####横轴-从数据个数得出模拟时长
x1_number= list(range(0,len(a1)));x05_number= list(range(0,len(a05)));x0_number = list(range(0,len(a0)))
x1_time = []; x05_time = []; x0_time = [];
for i in x1_number:
    x1_time.append(i*0.5/1000)
for i in x05_number:
    x05_time.append(i*0.5/1000)
for i in x0_number:
    x0_time.append(i*0.5/1000)

#####绘图
plt.figure()
plt.plot(x1_time,a1,'k',linewidth=0.5)
plt.plot(x1_time,b1,'b',linewidth=2)
plt.plot(x05_time,a05,'k',linewidth=0.5)
plt.plot(x05_time,b05,'b',linewidth=2)
plt.plot(x0_time,a0,'k',linewidth=0.5)
plt.plot(x0_time,b0,'b',linewidth=2)
plt.xticks(fontname='Times New Roman',fontsize=20),plt.yticks(fontname='Times New Roman',fontsize=20)
plt.title('Energy Gap',fontname='Times New Roman',fontsize=20)
font1 = {'family':'Times New Roman','weight':'normal','size':22}  ##设置legend的字体
plt.legend(['0.0','0.5','1.0'],prop=font1)
plt.xlim(0,)
plt.ylim(8,20)
plt.xlabel('Time (ps)',fontname='Times New Roman',fontsize=24)
plt.ylabel('Energy gap (eV)',fontname='Times New Roman',fontsize=24)
plt.grid()
plt.show()