import numpy as np
import matplotlib.pyplot as plt

def calculate_species_distribution_2pKa(pKa1, pKa2, pH_values):
    ratio1 = 10 ** (pH_values - pKa1)
    ratio2 = 10 ** (pH_values - pKa2)
    
    species_percentage_1 = ratio1 / (1 + ratio1) * 100
    species_percentage_2 = (ratio2 / ((1 + ratio2) * (1 + ratio1))) * 100
    
    return species_percentage_1, species_percentage_2

# 导入 pH 值范围 和 百分比
F = np.loadtxt('1.dat')

pH_values = F[:,4]   # pH 值列表
Species_A = F[:,3] * 10000
Species_B = F[:,5] * 10000
Species_C = F[:,6] * 10000

# 绘制曲线
plt.figure(figsize=(9/2.54, 8/2.54))
plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
plt.rcParams['svg.fonttype'] = 'none'

plt.plot(pH_values, Species_A, label='Species_A', linewidth=1)
plt.plot(pH_values, Species_B, label='Species_B', linewidth=1)
plt.plot(pH_values, Species_C, label='Species_C', linewidth=1)

# 设置 xy 轴范围和刻度
x_range=(2, 12)
plt.xlim(x_range)
plt.xticks(np.arange(x_range[0], x_range[1] + 1, 2))

y_range=(0, 100)
plt.ylim(y_range)

plt.xlabel('pH')
plt.ylabel('Species distribution (%)')
# plt.title('Species Distribution vs pH (2 pKa values)')
plt.legend()
plt.savefig('species.svg', bbox_inches='tight', format='svg')

# plt.grid(True)
plt.show()

