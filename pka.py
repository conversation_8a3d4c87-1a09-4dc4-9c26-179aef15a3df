"""
pKa 能量分析与结果写入Excel

作者: Hemengjia
日期: 2024-07-07
描述: 读取能量数据，计算累积平均与统计量，并写入Excel，绘制能量趋势图。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import openpyxl
import argparse

# 常量
HARTREE_TO_EV = 27.211396132

def cum_avg(en):
    """计算累积平均值"""
    return np.cumsum(en) / np.arange(1, len(en) + 1)

def load_energy_data(path, filename, skip=200):
    """加载能量数据文件，跳过前skip行"""
    filepath = os.path.join(path, filename)
    try:
        data = np.loadtxt(filepath)[skip:]
        return data
    except Exception as e:
        print(f"❌ 加载文件 {filename} 失败: {e}")
        return None

def calc_stats(en, u=100):
    """计算均值和半区间差"""
    mean_val = np.mean(en[u:])
    half_len = len(en) // 2
    diff = np.abs(np.mean(en[u:half_len]) - np.mean(en[half_len:]))
    return mean_val, diff

def main():
    parser = argparse.ArgumentParser(description='pKa 能量分析与结果写入Excel')
    parser.add_argument('--excel', help='Excel保存路径（含文件名）')
    parser.add_argument('--en1-start', type=int, default=None, help='en1数据起始索引（从0开始）')
    parser.add_argument('--en1-end', type=int, default=None, help='en1数据结束索引（不包含该索引）')
    parser.add_argument('--en05-start', type=int, default=None, help='en05数据起始索引（从0开始）')
    parser.add_argument('--en05-end', type=int, default=None, help='en05数据结束索引（不包含该索引）')
    parser.add_argument('--en0-start', type=int, default=None, help='en0数据起始索引（从0开始）')
    parser.add_argument('--en0-end', type=int, default=None, help='en0数据结束索引（不包含该索引）')
    args = parser.parse_args()

    # 读取并切片 en1、en05、en0
    en1_full = np.loadtxt('en1')
    en05_full = np.loadtxt('en05')
    en0_full = np.loadtxt('en0')
    en1 = en1_full[args.en1_start:args.en1_end] if args.en1_start is not None or args.en1_end is not None else en1_full
    en05 = en05_full[args.en05_start:args.en05_end] if args.en05_start is not None or args.en05_end is not None else en05_full
    en0 = en0_full[args.en0_start:args.en0_end] if args.en0_start is not None or args.en0_end is not None else en0_full

    # 计算均值和半区间差
    n1, d1 = calc_stats(en1)
    n05, d05 = calc_stats(en05)
    n0, d0 = calc_stats(en0)

    n1_ev = n1 * HARTREE_TO_EV
    n05_ev = n05 * HARTREE_TO_EV
    n0_ev = n0 * HARTREE_TO_EV
    d1_ev = d1 * HARTREE_TO_EV
    d05_ev = d05 * HARTREE_TO_EV
    d0_ev = d0 * HARTREE_TO_EV

    # 第4列为加权平均
    n_combined = [n1_ev, n05_ev, n0_ev, (n1_ev + n0_ev)/6 + 2/3 * n05_ev]
    d_combined = [d1_ev, d05_ev, d0_ev, (d1_ev + d0_ev)/6 + 2/3 * d05_ev]

    print(n_combined)
    print(d_combined)

    # 处理Excel保存路径
    if args.excel:
        excel_path = args.excel
    else:
        en0_dir = os.path.dirname(os.path.abspath('en0'))
        excel_path = os.path.join(en0_dir, 'pKa_result.xlsx')

    # 如果文件存在则加载，否则新建
    if os.path.exists(excel_path):
        wb = openpyxl.load_workbook(excel_path)
        if 'new' in wb.sheetnames:
            ws = wb['new']
        else:
            ws = wb.create_sheet('new')
    else:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = 'new'

    # 写入数据到单元格中
    for i in range(4):
        ws.cell(row=25, column=6+i).value = n_combined[i]
        ws.cell(row=26, column=6+i).value = d_combined[i]
        print(f"写入Excel: row25,col{6+i}={n_combined[i]:.6f}  row26,col{6+i}={d_combined[i]:.6f}")

    # 保存文件
    wb.save(excel_path)
    print(f"✅ 结果已保存到: {excel_path}")

    # 画图
    fig, axs = plt.subplots(2, 1)
    axs[0].plot(np.arange(len(en1)), cum_avg(en1) * HARTREE_TO_EV, '-r', linewidth=2)
    axs[0].plot(np.arange(len(en05)), cum_avg(en05) * HARTREE_TO_EV, '-m', linewidth=2)
    axs[0].plot(np.arange(len(en0)), cum_avg(en0) * HARTREE_TO_EV, '-b', linewidth=2)
    axs[0].set_title('1 H-bonds with solvating H2O')
    axs[1].plot(np.arange(len(en1))*0.5*0.001, en1 * HARTREE_TO_EV, '-r', linewidth=2)
    axs[1].plot(np.arange(len(en05))*0.5*0.001, en05 * HARTREE_TO_EV, '-m', linewidth=2)
    axs[1].plot(np.arange(len(en0))*0.5*0.001, en0 * HARTREE_TO_EV, '-b', linewidth=2)
    axs[1].set_title('1 H-bonds with solvating H2O')
    plt.show()

if __name__ == '__main__':
    main()


