'''
Author: MengjiaHe <EMAIL>
Date: 2023-10-26 13:20:58
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2024-01-19 11:39:23
FilePath: /workspace20240119 (工作区)/Users/<USER>/Documents/My_Library/inp/hmj_file/3-Resources/python脚本/plot2PMFcombined.py
Description: 
/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v1-H0/bi/plot5-4-bi-mono.py
Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import pandas as pd
# plt.rcParams['font.sans-serif'] = ['AppleGothic']  # 指定默认字体
# plt.rcParams['axes.unicode_minus'] = False  # 解决保存像时负号'-'显示为方块的问题


# 获取当前脚本所在目录
script_dir = os.path.dirname(os.path.realpath(__file__))

# 切换工作目录到脚本所在目录
os.chdir(script_dir)


# 读取第一段数据
data1 = np.loadtxt('1.dat', delimiter='\t')
x1 = data1[:, 0]  # 第一列为x值
y1 = data1[:, 6]  # 第七列为y值
y1_error = data1[:, 7]  # 第八列为误差值

# 反转x1的顺序
x1 = x1[::-1]
y1 = y1[::-1]
y1_error = y1_error[::-1]


# 绘制1.dat的error bar
# fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), sharey=True)
# fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 1.3]})
# Create subplots without outer frame
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5))
fig.subplots_adjust(wspace=0)  # 设置子图之间的水平空间为0

ax1.errorbar(x1, y1, yerr=y1_error, fmt='o-', linewidth=0.5, markersize=2.5, capsize=2, elinewidth=0.5, label='1.dat', color='black')
# ax1.set_title('1.dat Error Bar')
ax1.set_xlabel('CN')
ax1.set_ylabel('Energy (kcal/mol)')
# ax1.legend()

# 反转 x 轴
ax1.invert_xaxis()


# 读取第二段数据
data2 = np.loadtxt('2.dat', delimiter='\t')
x2 = data2[:, 0]  # 第一列为x值
x3=x2
# x2 = x2- (x2[0]- x1[0])
# print(x2)

y2 = data2[:, 6]  # 第二列为y值
y2_error = data2[:, 7]  # 第八列为误差值

# 绘制2.dat的error bar
ax2.errorbar(x2, y2, yerr=y2_error, fmt='o-', linewidth=0.5, markersize=2.5, capsize=2, elinewidth=0.5, label='2.dat', color='black')
# ax2.set_title('2.dat Error Bar')
ax2.set_xlabel('Distances (Å)')
# ax2.set_ylabel('Y')
# ax2.legend()
ax2.yaxis.set_visible(False)

# 调整布局
plt.tight_layout()

# 设置y轴范围相同
y_min = min(np.min(y1)-1, np.min(y2))
y_max = max(np.max(y1), np.max(y2)+2)

ax1.set_ylim(y_min, y_max)
ax1.set_xlim(1.01, 0.2)
ax2.set_ylim(y_min, y_max)
ax2.set_xlim(2.2, 4.9)

plt.rcParams.update({'font.size': 13.5})
# 设置 x1 轴的刻度位置和标签
x1_ticks = [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2]
ax1.set_xticks(x1_ticks)
ax1.set_xticklabels([str(t) for t in x1_ticks])

# 设置 x2 轴的刻度位置和标签
x2_ticks = [2.5, 3.0, 3.5, 4.0, 4.5]
ax2.set_xticks(x2_ticks)
ax2.set_xticklabels([str(t) for t in x2_ticks])

# # 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
# x_range=(2, 4.7)
# x_lim = 0.3
# plt.xlim(x_range)
# plt.xticks(np.arange(x_range[0], x_range[1] + x_lim, x_lim))


# 向下移动第二个 x 轴的标签
ax2.tick_params(axis='x', which='both', bottom=True, top=False, labelbottom=True, labeltop=False, pad=15)

ax1.spines['top'].set_visible(False)
ax1.spines['right'].set_visible(False)
ax1.spines['bottom'].set_linewidth(0.5)
ax1.spines['left'].set_linewidth(0.5)


ax2.spines['top'].set_visible(False)
ax2.spines['right'].set_visible(False)
ax2.spines['bottom'].set_linewidth(0.5)
ax2.spines['left'].set_linewidth(False)
# 设置图形边距为0
fig.subplots_adjust(left=0, right=1, bottom=0, top=1, wspace=0)
# 保存为 EPS 文件
# plt.savefig('combined_plots.eps', format='eps', bbox_inches='tight')
# plt.savefig('combined_plots.png', dpi=300, bbox_inches='tight')
# plt.rcParams['font.family'] = 'Arial'
# plt.rcParams['ps.fonttype'] = 42
# plt.rcParams['pdf.fonttype'] = 42
plt.rcParams['svg.fonttype'] = 'none'
# plt.rcParams['font.family'] = 'Times'  # 以Arial字体为例
plt.savefig('combined_plots-2.svg',bbox_inches='tight', format='svg')

# 显示像
plt.show()

