'''
Author: MengjiaHe <EMAIL>
Date: 2023-10-23 15:49:41
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2024-01-19 11:38:55
FilePath: /workspace20240119 (工作区)/Users/<USER>/Documents/My_Library/inp/hmj_file/3-Resources/python脚本/plotPMFfromdat.py
Description: plot energy profiles.

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''


import os
import numpy as np
# from openpyxl import load_workbook
import matplotlib
import matplotlib.pyplot as plt

# 设置字体大小种类和线条粗细
matplotlib.rcParams['axes.prop_cycle'] = matplotlib.cycler(color=['black'])
plt.rcParams['font.sans-serif'] = 'Helvetica'
# plt.rcParams['font.sans-serif'] = 'Times New Roman'
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 0.5

# 获取当前脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 切换工作目录到脚本所在目录
os.chdir(script_dir)

# 加载dat文件
data1 = np.loadtxt('1.dat', delimiter='\t')
x = data1[:, 0]  # 第一列为x值
y = data1[:, 6]  # 第七列为y值
y_error = data1[:, 7]  # 第八列为误差值

# 绘制图表
plt.errorbar(x, y, yerr=y_error, fmt='o-', linewidth=0.5, markersize=2.5, capsize=2, elinewidth=0.5, color='black')
plt.xlabel('Distances (Å)')
plt.ylabel('Energy (kcal/mol)')
plt.gca().spines['top'].set_visible(False)
plt.gca().spines['right'].set_visible(False)
plt.gca().spines['bottom'].set_linewidth(0.5)
plt.gca().spines['left'].set_linewidth(0.5)
plt.text(0.9, 0.75, '24.7 kcal/mol', ha='center', va='center', transform=plt.gca().transAxes)

# 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
x_range=(2, 4.7)
x_lim = 0.3
plt.xlim(x_range)
plt.xticks(np.arange(x_range[0], x_range[1] + x_lim, x_lim))

# 保存图形
# plt.savefig('plotPMF.png', dpi=300, bbox_inches='tight')

plt.rcParams['svg.fonttype'] = 'none'
plt.savefig('PMFplot.svg',bbox_inches='tight', format='svg')

# 显示图形
plt.show()
