'''
Author: MengjiaHe <EMAIL>
Date: 2023-10-26 13:20:58
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2024-01-19 11:38:26
FilePath: /workspace20240119 (工作区)/Users/<USER>/Documents/My_Library/inp/hmj_file/3-Resources/python脚本/plot_PMF_PPT.py
Description: 
/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v1-H0/bi/plot5-4-bi-mono.py
Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import pandas as pd


def load_data(file):
    data = np.loadtxt(file)
    x = data[:, 0]
    y = data[:, 4]
    y_error = data[:, 5]
    return x[::-1], y[::-1], y_error[::-1]


def plot_error_bar(x, y, y_error, xlabel='', ylabel=''):
    plt.rcParams['svg.fonttype'] = 'none'
    # plt.figure(figsize=(9/2.54, 8/2.54))#word
    plt.figure(figsize=(33.867/2.54*0.7, 19.05/2.54*0.7))  # PPT
    plt.rcParams.update(
        {'font.size': 20, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
      # {'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
    plt.errorbar(x, y, yerr=y_error, fmt='o-', linewidth=0.5, markersize=2.5,
                 capsize=2, elinewidth=0.5, label='1.dat', color='black')
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    # plt.gca().invert_xaxis()  # 反转 x 轴
    # # 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
    # plt.xlim(2.3, 4.1)
    # plt.xticks(np.arange(2.4, 4.1, 0.2))  # 9 表示结束值（不包含在内）
    # 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
    x_range=(2, 4.7)
    x_lim = 0.3
    plt.xlim(x_range)
    plt.xticks(np.arange(x_range[0], x_range[1] + x_lim, x_lim))

    #plt.savefig('PMF_plot_word.svg', bbox_inches='tight', format='svg')

    plt.savefig('PMF_plot_PPT.svg', bbox_inches='tight', format='svg')
    plt.show()


# 使用函数加载数据并绘图
file_path = 'PMF.dat'
x_data, y_data, y_error_data = load_data(file_path)
Ang = 'Å'
plot_error_bar(x_data, y_data, y_error_data, xlabel=f'Distance ({Ang})',
               ylabel='Free energy (kcal/mol)')
