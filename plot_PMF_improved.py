#!/usr/bin/env python3
"""
PMF绘图脚本 (改进版) - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 绘制势能面(PMF)图形，支持多种输出格式和自定义参数

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path
import argparse
import logging
import numpy as np

# 添加工具库路径
sys.path.append(str(Path(__file__).parent / 'hmj-py-lib'))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PMFPlotter:
    """PMF绘图器类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化PMF绘图器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.data_loader = DataLoader()
        self.plot_manager = PlotManager(self.config.get_plot_config())
        
        logger.info("📊 PMF绘图器初始化完成")
    
    def plot_pmf(self, 
                 data_file: str,
                 output_file: str = None,
                 output_format: str = 'both',  # 'word', 'ppt', 'both'
                 x_range: tuple = None,
                 x_tick_interval: float = None,
                 invert_x: bool = None) -> dict:
        """
        绘制PMF图形
        
        Args:
            data_file: PMF数据文件路径
            output_file: 输出文件路径（不含扩展名）
            output_format: 输出格式 ('word', 'ppt', 'both')
            x_range: x轴范围
            x_tick_interval: x轴刻度间隔
            invert_x: 是否反转x轴
            
        Returns:
            dict: 绘图结果
        """
        logger.info(f"🚀 开始PMF绘图: {data_file}")
        
        try:
            # 1. 加载PMF数据
            x_data, y_data, y_error = self.data_loader.load_pmf_data(data_file)
            
            # 验证数据
            for data, name in [(x_data, 'x'), (y_data, 'y'), (y_error, 'y_error')]:
                if not self.data_loader.validate_data(data, check_finite=True):
                    raise ValueError(f"PMF {name} 数据验证失败")
            
            # 2. 设置默认参数
            pmf_config = self.config.get('analysis_parameters.pmf_plotting', {})
            
            if x_range is None:
                x_range = tuple(pmf_config.get('x_range', [1, 0.1]))
            
            if x_tick_interval is None:
                x_tick_interval = pmf_config.get('x_tick_interval', 0.2)
            
            if invert_x is None:
                invert_x = pmf_config.get('invert_x', True)
            
            if output_file is None:
                output_file = Path(data_file).stem + '_PMF'
            
            # 3. 计算统计信息
            stats = self._calculate_statistics(x_data, y_data, y_error)
            
            # 4. 绘制不同格式的图形
            output_files = []
            
            if output_format in ['word', 'both']:
                word_file = self._plot_for_word(x_data, y_data, y_error, x_range, 
                                              x_tick_interval, invert_x, f"{output_file}_word.svg")
                output_files.append(word_file)
            
            if output_format in ['ppt', 'both']:
                ppt_file = self._plot_for_ppt(x_data, y_data, y_error, x_range, 
                                            x_tick_interval, invert_x, f"{output_file}_ppt.svg")
                output_files.append(ppt_file)
            
            logger.info("✅ PMF绘图完成!")
            
            return {
                'x_data': x_data,
                'y_data': y_data,
                'y_error': y_error,
                'statistics': stats,
                'output_files': output_files
            }
            
        except Exception as e:
            logger.error(f"❌ PMF绘图失败: {e}")
            raise
    
    def _plot_for_word(self, x_data, y_data, y_error, x_range, x_tick_interval, invert_x, output_file):
        """为Word文档绘制PMF图形"""
        # Word格式配置 (较小尺寸)
        word_config = {
            'figure_size_cm': (9/2.54*2.54, 8/2.54*2.54),  # 转换为厘米
            'font_size': 12
        }
        
        plot_manager = PlotManager(word_config)
        
        fig = plot_manager.plot_pmf(
            x_data, y_data, y_error,
            xlabel='CN',
            ylabel='Free energy (kcal/mol)',
            x_range=x_range,
            x_tick_interval=x_tick_interval,
            invert_x=invert_x
        )
        
        plot_manager.save_figure(fig, output_file)
        logger.info(f"📄 Word格式图形已保存: {output_file}")
        
        return output_file
    
    def _plot_for_ppt(self, x_data, y_data, y_error, x_range, x_tick_interval, invert_x, output_file):
        """为PPT演示绘制PMF图形"""
        # PPT格式配置 (较大尺寸，大字体)
        ppt_config = {
            'figure_size_cm': (33.867/2.54*0.7*2.54, 19.05/2.54*0.7*2.54),  # 转换为厘米
            'font_size': 20
        }
        
        plot_manager = PlotManager(ppt_config)
        
        fig = plot_manager.plot_pmf(
            x_data, y_data, y_error,
            xlabel='CN',
            ylabel='Free energy (kcal/mol)',
            x_range=x_range,
            x_tick_interval=x_tick_interval,
            invert_x=invert_x
        )
        
        plot_manager.save_figure(fig, output_file)
        logger.info(f"📊 PPT格式图形已保存: {output_file}")
        
        return output_file
    
    def _calculate_statistics(self, x_data, y_data, y_error):
        """计算PMF统计信息"""
        stats = {
            'x_range': (np.min(x_data), np.max(x_data)),
            'y_range': (np.min(y_data), np.max(y_data)),
            'min_energy': np.min(y_data),
            'max_energy': np.max(y_data),
            'energy_barrier': np.max(y_data) - np.min(y_data),
            'mean_error': np.mean(y_error),
            'max_error': np.max(y_error),
            'n_points': len(x_data)
        }
        
        logger.info("📈 PMF统计:")
        logger.info(f"   CN范围: {stats['x_range'][0]:.3f} - {stats['x_range'][1]:.3f}")
        logger.info(f"   能量范围: {stats['y_range'][0]:.3f} - {stats['y_range'][1]:.3f} kcal/mol")
        logger.info(f"   能量势垒: {stats['energy_barrier']:.3f} kcal/mol")
        logger.info(f"   平均误差: {stats['mean_error']:.3f} kcal/mol")
        logger.info(f"   数据点数: {stats['n_points']}")
        
        return stats
    
    def compare_pmf_files(self, data_files: list, output_file: str = 'PMF_comparison.svg') -> dict:
        """
        比较多个PMF文件
        
        Args:
            data_files: PMF数据文件列表
            output_file: 输出比较图文件
            
        Returns:
            dict: 比较结果
        """
        logger.info(f"🔄 开始PMF比较: {len(data_files)} 个文件")
        
        try:
            import matplotlib.pyplot as plt
            
            fig = self.plot_manager.create_figure()
            colors = self.plot_manager.config['colors']
            
            all_data = {}
            
            for i, data_file in enumerate(data_files):
                logger.info(f"📁 加载文件 {i+1}/{len(data_files)}: {Path(data_file).name}")
                
                x_data, y_data, y_error = self.data_loader.load_pmf_data(data_file)
                
                color = colors[i % len(colors)]
                label = Path(data_file).stem
                
                plt.errorbar(x_data, y_data, yerr=y_error,
                           fmt='o-', linewidth=1.0, markersize=2.5,
                           capsize=2, elinewidth=0.5,
                           color=color, label=label)
                
                all_data[data_file] = {
                    'x_data': x_data,
                    'y_data': y_data,
                    'y_error': y_error
                }
            
            plt.xlabel('CN')
            plt.ylabel('Free energy (kcal/mol)')
            plt.legend()
            
            self.plot_manager.save_figure(fig, output_file)
            plt.show()
            
            logger.info(f"✅ PMF比较完成: {output_file}")
            
            return {
                'all_data': all_data,
                'output_file': output_file,
                'n_files': len(data_files)
            }
            
        except Exception as e:
            logger.error(f"❌ PMF比较失败: {e}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PMF绘图工具 (改进版)')
    parser.add_argument('data_file', help='PMF数据文件路径')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--output', '-o', help='输出文件路径（不含扩展名）')
    parser.add_argument('--format', '-f', choices=['word', 'ppt', 'both'], 
                       default='both', help='输出格式')
    parser.add_argument('--x-range', nargs=2, type=float, help='x轴范围')
    parser.add_argument('--x-interval', type=float, help='x轴刻度间隔')
    parser.add_argument('--no-invert-x', action='store_true', help='不反转x轴')
    parser.add_argument('--compare', nargs='+', help='比较多个PMF文件')
    
    args = parser.parse_args()
    
    try:
        # 创建绘图器
        plotter = PMFPlotter(args.config)
        
        if args.compare:
            # 比较模式
            results = plotter.compare_pmf_files(
                args.compare,
                args.output or 'PMF_comparison.svg'
            )
            
            print(f"\n🎉 PMF比较完成!")
            print(f"   比较文件数: {results['n_files']}")
            print(f"   输出文件: {results['output_file']}")
            
        else:
            # 单文件绘图模式
            results = plotter.plot_pmf(
                args.data_file,
                output_file=args.output,
                output_format=args.format,
                x_range=tuple(args.x_range) if args.x_range else None,
                x_tick_interval=args.x_interval,
                invert_x=not args.no_invert_x
            )
            
            print(f"\n🎉 PMF绘图完成!")
            print(f"   能量势垒: {results['statistics']['energy_barrier']:.3f} kcal/mol")
            print(f"   数据点数: {results['statistics']['n_points']}")
            print(f"   输出文件: {', '.join(results['output_files'])}")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
