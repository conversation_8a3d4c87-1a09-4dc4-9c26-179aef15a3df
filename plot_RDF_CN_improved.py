#!/usr/bin/env python3
"""
RDF和CN绘图脚本 (改进版) - 使用HMJ工具库

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 绘制径向分布函数(RDF)和配位数(CN)图形，支持双y轴显示和自定义参数

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path
import argparse
import logging
import numpy as np

# 添加工具库路径
sys.path.append(str(Path(__file__).parent / 'hmj-py-lib'))

from data_loader import DataLoader
from plotting import PlotManager
from config_manager import ConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RDFCNPlotter:
    """RDF和CN绘图器类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化RDF/CN绘图器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.data_loader = DataLoader()
        self.plot_manager = PlotManager(self.config.get_plot_config())
        
        logger.info("📊 RDF/CN绘图器初始化完成")
    
    def plot_rdf_cn(self, 
                    data_file: str,
                    output_file: str = None,
                    x_range: tuple = None,
                    rdf_ylim: tuple = None,
                    cn_ylim: tuple = None,
                    title: str = None) -> dict:
        """
        绘制RDF和CN图形
        
        Args:
            data_file: RDF/CN数据文件路径
            output_file: 输出文件路径
            x_range: x轴范围
            rdf_ylim: RDF y轴范围
            cn_ylim: CN y轴范围
            title: 图形标题
            
        Returns:
            dict: 绘图结果
        """
        logger.info(f"🚀 开始RDF/CN绘图: {data_file}")
        
        try:
            # 1. 加载RDF/CN数据
            distance, rdf, cn = self.data_loader.load_rdf_data(data_file)
            
            # 验证数据
            for data, name in [(distance, 'distance'), (rdf, 'rdf'), (cn, 'cn')]:
                if not self.data_loader.validate_data(data, check_finite=True):
                    raise ValueError(f"RDF/CN {name} 数据验证失败")
            
            # 2. 设置默认参数
            rdf_config = self.config.get('analysis_parameters.rdf_cn_plotting', {})
            
            if x_range is None:
                x_range = tuple(rdf_config.get('x_range', [2, 4.7]))
            
            if rdf_ylim is None:
                rdf_ylim = tuple(rdf_config.get('rdf_ylim', [0, 3]))
            
            if cn_ylim is None:
                cn_ylim = tuple(rdf_config.get('cn_ylim', [0, 4]))
            
            if output_file is None:
                output_file = Path(data_file).with_suffix('.svg').name
            
            if title is None:
                title = f"RDF and CN Analysis - {Path(data_file).stem}"
            
            # 3. 计算统计信息
            stats = self._calculate_statistics(distance, rdf, cn, x_range)
            
            # 4. 绘制图形
            fig = self.plot_manager.plot_rdf_cn(
                distance, rdf, cn,
                x_range=x_range,
                rdf_ylim=rdf_ylim,
                cn_ylim=cn_ylim
            )
            
            # 5. 添加标题
            if title:
                import matplotlib.pyplot as plt
                plt.title(title)
            
            # 6. 保存和显示
            self.plot_manager.show_and_save(fig, output_file, show=True)
            
            logger.info("✅ RDF/CN绘图完成!")
            
            return {
                'distance': distance,
                'rdf': rdf,
                'cn': cn,
                'statistics': stats,
                'output_file': output_file
            }
            
        except Exception as e:
            logger.error(f"❌ RDF/CN绘图失败: {e}")
            raise
    
    def _calculate_statistics(self, distance, rdf, cn, x_range):
        """计算RDF/CN统计信息"""
        # 筛选指定范围内的数据
        mask = (distance >= x_range[0]) & (distance <= x_range[1])
        distance_range = distance[mask]
        rdf_range = rdf[mask]
        cn_range = cn[mask]
        
        # 找到RDF峰值
        rdf_max_idx = np.argmax(rdf_range)
        rdf_peak_distance = distance_range[rdf_max_idx]
        rdf_peak_value = rdf_range[rdf_max_idx]
        
        # 计算最终配位数
        final_cn = cn_range[-1] if len(cn_range) > 0 else cn[-1]
        
        stats = {
            'distance_range': (distance.min(), distance.max()),
            'analysis_range': x_range,
            'rdf_peak': {
                'distance': rdf_peak_distance,
                'value': rdf_peak_value
            },
            'rdf_stats': {
                'mean': np.mean(rdf_range),
                'max': np.max(rdf_range),
                'min': np.min(rdf_range)
            },
            'cn_stats': {
                'final': final_cn,
                'mean': np.mean(cn_range),
                'max': np.max(cn_range),
                'min': np.min(cn_range)
            },
            'n_points': len(distance),
            'n_points_in_range': len(distance_range)
        }
        
        logger.info("📈 RDF/CN统计:")
        logger.info(f"   距离范围: {stats['distance_range'][0]:.3f} - {stats['distance_range'][1]:.3f} Å")
        logger.info(f"   分析范围: {stats['analysis_range'][0]:.3f} - {stats['analysis_range'][1]:.3f} Å")
        logger.info(f"   RDF峰值: {stats['rdf_peak']['value']:.3f} @ {stats['rdf_peak']['distance']:.3f} Å")
        logger.info(f"   最终配位数: {stats['cn_stats']['final']:.3f}")
        logger.info(f"   数据点数: {stats['n_points']} (分析范围内: {stats['n_points_in_range']})")
        
        return stats
    
    def compare_rdf_cn_files(self, data_files: list, output_file: str = 'RDF_CN_comparison.svg') -> dict:
        """
        比较多个RDF/CN文件
        
        Args:
            data_files: RDF/CN数据文件列表
            output_file: 输出比较图文件
            
        Returns:
            dict: 比较结果
        """
        logger.info(f"🔄 开始RDF/CN比较: {len(data_files)} 个文件")
        
        try:
            import matplotlib.pyplot as plt
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(8, 10))
            colors = self.plot_manager.config['colors']
            
            all_data = {}
            
            for i, data_file in enumerate(data_files):
                logger.info(f"📁 加载文件 {i+1}/{len(data_files)}: {Path(data_file).name}")
                
                distance, rdf, cn = self.data_loader.load_rdf_data(data_file)
                
                color = colors[i % len(colors)]
                label = Path(data_file).stem
                
                # 绘制RDF
                ax1.plot(distance, rdf, color=color, label=label, linewidth=1.0)
                ax1.set_ylabel('RDF')
                ax1.set_xlabel('Distance (Å)')
                ax1.legend()
                ax1.grid(True, alpha=0.3)
                
                # 绘制CN
                ax2.plot(distance, cn, color=color, label=label, linewidth=1.0)
                ax2.set_ylabel('CN')
                ax2.set_xlabel('Distance (Å)')
                ax2.legend()
                ax2.grid(True, alpha=0.3)
                
                all_data[data_file] = {
                    'distance': distance,
                    'rdf': rdf,
                    'cn': cn
                }
            
            plt.tight_layout()
            self.plot_manager.save_figure(fig, output_file)
            plt.show()
            
            logger.info(f"✅ RDF/CN比较完成: {output_file}")
            
            return {
                'all_data': all_data,
                'output_file': output_file,
                'n_files': len(data_files)
            }
            
        except Exception as e:
            logger.error(f"❌ RDF/CN比较失败: {e}")
            raise
    
    def batch_plot(self, data_files: list, output_dir: str = None) -> dict:
        """
        批量绘制RDF/CN图形
        
        Args:
            data_files: RDF/CN数据文件列表
            output_dir: 输出目录
            
        Returns:
            dict: 批量绘图结果
        """
        logger.info(f"🔄 开始批量RDF/CN绘图: {len(data_files)} 个文件")
        
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
        
        results = {}
        failed_files = []
        
        for i, data_file in enumerate(data_files, 1):
            try:
                logger.info(f"📁 处理文件 {i}/{len(data_files)}: {Path(data_file).name}")
                
                # 设置输出文件路径
                if output_dir:
                    output_file = output_path / f"{Path(data_file).stem}_rdf_cn.svg"
                else:
                    output_file = f"{Path(data_file).stem}_rdf_cn.svg"
                
                # 执行绘图
                result = self.plot_rdf_cn(
                    data_file,
                    str(output_file),
                    title=f"RDF and CN - {Path(data_file).stem}"
                )
                
                results[data_file] = result
                logger.info(f"✅ 文件处理成功: {Path(data_file).name}")
                
            except Exception as e:
                logger.error(f"❌ 文件处理失败: {Path(data_file).name} - {e}")
                failed_files.append(data_file)
        
        logger.info(f"📊 批量绘图完成: 成功 {len(results)}, 失败 {len(failed_files)}")
        
        return {
            'results': results,
            'failed_files': failed_files,
            'success_count': len(results),
            'failed_count': len(failed_files)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RDF/CN绘图工具 (改进版)')
    parser.add_argument('data_file', help='RDF/CN数据文件路径')
    parser.add_argument('--config', '-c', default='config.yaml', help='配置文件路径')
    parser.add_argument('--output', '-o', help='输出图形文件路径')
    parser.add_argument('--title', '-t', help='图形标题')
    parser.add_argument('--x-range', nargs=2, type=float, help='x轴范围')
    parser.add_argument('--rdf-ylim', nargs=2, type=float, help='RDF y轴范围')
    parser.add_argument('--cn-ylim', nargs=2, type=float, help='CN y轴范围')
    parser.add_argument('--compare', nargs='+', help='比较多个RDF/CN文件')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    parser.add_argument('--output-dir', help='批量处理输出目录')
    
    args = parser.parse_args()
    
    try:
        # 创建绘图器
        plotter = RDFCNPlotter(args.config)
        
        if args.compare:
            # 比较模式
            results = plotter.compare_rdf_cn_files(
                args.compare,
                args.output or 'RDF_CN_comparison.svg'
            )
            
            print(f"\n🎉 RDF/CN比较完成!")
            print(f"   比较文件数: {results['n_files']}")
            print(f"   输出文件: {results['output_file']}")
            
        elif args.batch:
            # 批量处理模式
            from hmj_py_lib.file_utils import FileUtils
            
            data_files = FileUtils.find_files(
                Path(args.data_file).parent,
                pattern="*rdfcn*.dat",
                recursive=False
            )
            
            if not data_files:
                logger.error("❌ 未找到RDF/CN数据文件")
                sys.exit(1)
            
            results = plotter.batch_plot(
                [str(f) for f in data_files],
                args.output_dir
            )
            
            print(f"\n🎉 批量绘图完成!")
            print(f"   成功处理: {results['success_count']} 个文件")
            print(f"   失败: {results['failed_count']} 个文件")
            
        else:
            # 单文件绘图模式
            results = plotter.plot_rdf_cn(
                args.data_file,
                output_file=args.output,
                x_range=tuple(args.x_range) if args.x_range else None,
                rdf_ylim=tuple(args.rdf_ylim) if args.rdf_ylim else None,
                cn_ylim=tuple(args.cn_ylim) if args.cn_ylim else None,
                title=args.title
            )
            
            print(f"\n🎉 RDF/CN绘图完成!")
            print(f"   RDF峰值: {results['statistics']['rdf_peak']['value']:.3f} @ {results['statistics']['rdf_peak']['distance']:.3f} Å")
            print(f"   最终配位数: {results['statistics']['cn_stats']['final']:.3f}")
            print(f"   输出文件: {results['output_file']}")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
