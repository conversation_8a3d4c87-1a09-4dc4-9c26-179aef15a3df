#!/opt/miniconda3/envs/Plot.conda/bin/python
"""
简化版RDF/CN绘图脚本

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 绘制径向分布函数(RDF)和配位数(CN)图形，无外部依赖
"""

import numpy as np
import matplotlib.pyplot as plt
import argparse
import sys
from pathlib import Path
import matplotlib.pyplot as plt
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'sans-serif',
    'font.sans-serif': ['Helvetica'] #'Arial']
})

def load_rdf_data(filename):
    """
    加载RDF/CN数据文件
    
    Args:
        filename (str): 数据文件路径
        
    Returns:
        tuple: (distance, rdf, cn) 数据数组
    """
    try:
        data = np.loadtxt(filename)
        print(f"✅ 成功加载数据文件: {filename}")
        print(f"   数据形状: {data.shape}")
        
        if data.ndim == 1:
            # 如果是一维数据，假设是距离数据
            distance = np.arange(len(data)) * 0.1  # 假设步长0.1 Å
            rdf = data
            cn = np.cumsum(rdf) * 0.1  # 简单的累积
            print("   检测到一维数据，假设为RDF数据")
        elif data.shape[1] == 2:
            # 两列数据：距离和RDF
            distance = data[:, 0]
            rdf = data[:, 1]
            cn = np.cumsum(rdf) * (distance[1] - distance[0])  # 数值积分
            print("   检测到两列数据: 距离, RDF")
        elif data.shape[1] >= 3:
            # 三列或更多：距离、RDF、CN
            distance = data[:, 0]
            rdf = data[:, 1]
            cn = data[:, 2]
            print("   检测到三列数据: 距离, RDF, CN")
        else:
            raise ValueError("数据格式不支持")
            
        print(f"   距离范围: {distance.min():.3f} - {distance.max():.3f} Å")
        print(f"   RDF范围: {rdf.min():.3f} - {rdf.max():.3f}")
        print(f"   CN范围: {cn.min():.3f} - {cn.max():.3f}")
        
        return distance, rdf, cn
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        sys.exit(1)


def plot_rdf_cn(distance, rdf, cn, 
                x_range=None, rdf_ylim=None, cn_ylim=None,
                output_file=None, xticks=None):
    """
    绘制RDF和CN图形
    
    Args:
        distance: 距离数据
        rdf: RDF数据
        cn: CN数据
        x_range: x轴范围
        rdf_ylim: RDF y轴范围
        cn_ylim: CN y轴范围
        output_file: 输出文件名
        xticks: x轴刻度列表
    """
    
    fig_width_cm, fig_height_cm = 9, 7
    fig_width_inch = fig_width_cm / 2.54
    fig_height_inch = fig_height_cm / 2.54
    # 创建双y轴图形
    fig, ax1 = plt.subplots(figsize=(fig_width_inch, fig_height_inch))
    
    # 绘制RDF (左y轴)
    color1 = 'black'
    ax1.set_xlabel('Distance (Å)')
    ax1.set_ylabel('RDF')#, color=color1)
    line1 = ax1.plot(distance, rdf, color=color1, linewidth=1, label='RDF')
    ax1.tick_params(axis='y')#, labelcolor=color1)
    
    # 创建右y轴绘制CN
    ax2 = ax1.twinx()
    color2 = 'black'
    ax2.set_ylabel('CN')#, color=color2)
    line2 = ax2.plot(distance, cn, color=color2, linewidth=1, label='CN',linestyle='--')
    ax2.tick_params(axis='y')#, labelcolor=color2)
    
    # 设置范围
    if x_range:
        ax1.set_xlim(x_range)
    if xticks is not None:
        ax1.set_xticks(xticks)
    if rdf_ylim:
        ax1.set_ylim(rdf_ylim)
    
    if cn_ylim:
        ax2.set_ylim(cn_ylim)
    
    # 关闭网格
    ax1.grid(False)
    ax2.grid(False)
    
    # 添加图例
   #  lines = line1 + line2
   #  labels = [l.get_label() for l in lines]
   #  ax1.legend(lines, labels, loc='upper left')
    
    # 不添加标题
    
    # 调整布局
    plt.tight_layout()
    # 设置字体和样式

    plt.rcParams['svg.fonttype'] = 'none'  # 确保SVG中的文字可编辑
    # 保存图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"💾 图形已保存: {output_file}")
    
    # 显示图形
    plt.show()
    
    return fig


def calculate_statistics(distance, rdf, cn, x_range=None):
    """计算统计信息"""
    if x_range:
        mask = (distance >= x_range[0]) & (distance <= x_range[1])
        distance_range = distance[mask]
        rdf_range = rdf[mask]
        cn_range = cn[mask]
    else:
        distance_range = distance
        rdf_range = rdf
        cn_range = cn
    
    # 找到RDF峰值
    if len(rdf_range) > 0:
        rdf_max_idx = np.argmax(rdf_range)
        rdf_peak_distance = distance_range[rdf_max_idx]
        rdf_peak_value = rdf_range[rdf_max_idx]
    else:
        rdf_peak_distance = 0
        rdf_peak_value = 0
    
    # 最终配位数
    final_cn = cn_range[-1] if len(cn_range) > 0 else cn[-1]
    
    print("\n📊 统计信息:")
    print(f"   RDF峰值: {rdf_peak_value:.3f} @ {rdf_peak_distance:.3f} Å")
    print(f"   最终配位数: {final_cn:.3f}")
    print(f"   分析数据点数: {len(distance_range)}")
    
    return {
        'rdf_peak_distance': rdf_peak_distance,
        'rdf_peak_value': rdf_peak_value,
        'final_cn': final_cn
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版RDF/CN绘图工具')
    parser.add_argument('data_file', help='RDF/CN数据文件路径')
    parser.add_argument('--output', '-o', help='输出图形文件路径')
    parser.add_argument('--title', '-t', help='图形标题')
    parser.add_argument('--x-range', nargs=2, type=float, help='x轴范围')
    parser.add_argument('--rdf-ylim', nargs=2, type=float, help='RDF y轴范围')
    parser.add_argument('--cn-ylim', nargs=2, type=float, help='CN y轴范围')
    parser.add_argument('--xticks', nargs='+', type=float, help='自定义x轴刻度（空格分隔）')
    
    args = parser.parse_args()
    
    print("🚀 开始RDF/CN分析...")
    print("=" * 50)
    
    try:
        # 1. 加载数据
        distance, rdf, cn = load_rdf_data(args.data_file)
        
        # 2. 设置默认参数
        output_file = args.output
        if not output_file:
            output_file = Path(args.data_file).with_suffix('.svg').name
        
        x_range = tuple(args.x_range) if args.x_range else None
        rdf_ylim = tuple(args.rdf_ylim) if args.rdf_ylim else None
        cn_ylim = tuple(args.cn_ylim) if args.cn_ylim else None
        xticks = args.xticks if args.xticks else None
        
        # 3. 计算统计信息
        stats = calculate_statistics(distance, rdf, cn, x_range)
        
        # 4. 绘制图形
        fig = plot_rdf_cn(
            distance, rdf, cn,
            x_range=x_range,
            rdf_ylim=rdf_ylim,
            cn_ylim=cn_ylim,
            output_file=output_file,
            xticks=xticks
        )
        
        print("=" * 50)
        print("✅ 分析完成!")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
