'''
Author: MengjiaHe <EMAIL>
Date: 2025-07-07 22:22:03
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-13 20:15:26
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Sb3/prolong/FreeMD/sb_oh_3-free/plot_sb_o_bonds_2025.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
#!/opt/miniconda3/envs/Plot.conda/bin/python
"""
画出 Sb-O1, Sb-O2, Sb-O3, Sb-O4 四条瞬时键长随时间(ps)的变化曲线
"""

import numpy as np
import matplotlib.pyplot as plt

def read_bond_data(filename):
    data = np.loadtxt(filename)
    time_steps = data[:, 0]
    bond_lengths = data[:, 1]
    return time_steps, bond_lengths

def setup_plot_style():
    fig_width_cm, fig_height_cm = 10, 8
    fig_width_inch = fig_width_cm / 2.54
    fig_height_inch = fig_height_cm / 2.54
    plt.figure(figsize=(fig_width_inch, fig_height_inch))
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'serif',
        'font.serif': ['Times New Roman']
    })
    plt.rcParams['svg.fonttype'] = 'none'

def main():
    files = ['Sb-O1.dat', 'Sb-O2.dat', 'Sb-O3.dat', 'Sb-O4.dat']
    labels = ['Sb-O1', 'Sb-O2', 'Sb-O3', 'Sb-O4']
    colors = ['blue', 'red', 'green', 'purple']

    setup_plot_style()
    averages = []

    for filename, label, color in zip(files, labels, colors):
        try:
            time_steps, bond_lengths = read_bond_data(filename)
            time_ps = time_steps / 200
         #  # 对Sb-O4，截去前3ps的数据点，并让曲线从0ps开始
         #  if label == 'Sb-O4':
         #      mask = time_ps >= 2
         #      time_ps = time_ps[mask]
         #      bond_lengths = bond_lengths[mask]
         #      # 重新归零x轴
         #      time_ps = time_ps - time_ps[0]
         #  if label == 'Sb-O3':
         #      mask = time_ps >= 2
         #      time_ps = time_ps[mask]
         #      bond_lengths = bond_lengths[mask]
         #      # 重新归零x轴
         #      time_ps = time_ps - time_ps[0]
         #  if label == 'Sb-O2':
         #      mask = time_ps >= 2
         #      time_ps = time_ps[mask]
         #      bond_lengths = bond_lengths[mask]
         #      # 重新归零x轴
         #      time_ps = time_ps - time_ps[0]
         #  if label == 'Sb-O1':
         #      mask = time_ps >= 2
         #      time_ps = time_ps[mask]
         #      bond_lengths = bond_lengths[mask]
         #      # 重新归零x轴
         #      time_ps = time_ps - time_ps[0]
            plt.plot(time_ps, bond_lengths, color=color, linewidth=0.8, label=label)
            avg = np.mean(bond_lengths)
            averages.append(avg)
            print(f"{label}: Mean bond length = {avg:.4f} Å")
        except Exception as e:
            print(f"Error processing {filename}: {e}")

    plt.xlabel('Time (ps)', fontsize=12)
    plt.ylabel('Sb-O Bond Length (Å)', fontsize=12)
    # plt.title('Sb-O Instantaneous Bond Lengths vs Time', fontsize=14, fontweight='bold')
    plt.legend(fontsize=10, loc='best')
    # plt.grid(True, alpha=0.3)

    if averages:
        y_min = min(averages) - 0.1
        y_max = max(averages) + 2
        plt.ylim(1.5, 5.5)
        plt.xlim(0, 18)
    
    plt.tight_layout()
    plt.savefig('sb_o_bond_lengths.svg', bbox_inches='tight', format='svg', dpi=300)
    # plt.savefig('sb_o_bond_lengths.png', dpi=300, bbox_inches='tight')
    # plt.savefig('sb_o_bond_lengths.pdf', bbox_inches='tight')
    plt.show()

    print("\n" + "="*50)
    print("SUMMARY STATISTICS")
    print("="*50)
    for label, avg in zip(labels, averages):
        print(f"{label}: {avg:.4f} Å")
    if len(averages) > 1:
        print(f"\nRange: {max(averages) - min(averages):.4f} Å")
        print(f"Overall average: {np.mean(averages):.4f} Å")

if __name__ == "__main__":
    main()
