#!/usr/bin/env python3
"""
快速测试脚本 - 验证文本处理功能
"""

import re

def process_literature_text(text):
    """
    处理文献文本的完整流程
    
    1. 将"【XX】"开头的行转换为"### XX"格式，并另起一行
    2. 在每个"###"行后添加前面最近的"## "行内容，转换为"#### "格式
    3. 删除所有"## "开头的行
    """
    
    lines = text.split('\n')
    
    # 步骤1: 转换【】为### 并记录## 标题
    step1_lines = []
    last_heading = None
    converted_count = 0
    
    for line in lines:
        if line.startswith('## '):
            # 记录最近的标题
            last_heading = line[3:].strip()
            step1_lines.append(line)  # 暂时保留
        else:
            # 匹配【内容】格式
            match = re.match(r'^【([^】]+)】(.*)$', line.strip())
            if match:
                bracket_content = match.group(1)
                remaining_content = match.group(2)
                # 转换为### 格式，另起一行
                new_line = f"### {bracket_content}{remaining_content}"
                step1_lines.append(new_line)
                converted_count += 1
            else:
                step1_lines.append(line)
    
    print(f"步骤1: 转换了 {converted_count} 个【】为### 格式")
    
    # 步骤2: 在### 后添加#### 标题
    step2_lines = []
    last_heading = None
    copied_count = 0
    
    for line in step1_lines:
        if line.startswith('## '):
            # 更新最近的标题
            last_heading = line[3:].strip()
            step2_lines.append(line)  # 暂时保留
        elif line.startswith('###'):
            # 添加### 行
            step2_lines.append(line)
            if last_heading:
                # 添加#### 标题
                step2_lines.append(f"#### {last_heading}")
                copied_count += 1
            step2_lines.append("")  # 添加空行
        else:
            step2_lines.append(line)
    
    print(f"步骤2: 复制了 {copied_count} 个标题为#### 格式")
    
    # 步骤3: 删除所有## 行
    final_lines = []
    removed_count = 0
    
    for line in step2_lines:
        if line.startswith('## '):
            removed_count += 1
        else:
            final_lines.append(line)
    
    print(f"步骤3: 删除了 {removed_count} 个## 标题行")
    
    return '\n'.join(final_lines)


def main():
    """主测试函数"""
    
    # 创建测试内容
    test_content = """## (1) 第一篇文献标题
【Mo V 污染报道不常见 来源】Environmental issues with molybdenum (Mo) and vanadium (V) have not often been reported.
【都有合金的应用】Both metals are widely used in high-performance alloyed steel production.
【V应用】Future developments in vanadium usage, such as the growing adoption of V-based sodium-ion batteries.

## (2) 第二篇文献标题  
【Mo污染 饮用水标准】Molybdenum (Mo) is an emerging contaminant and at high concentration can pose a significant environmental hazard.
【Mo污染具体场景 浓度】Mo contamination can occur in a variety of scenarios from mine drainage/wastes.
【Mo 具体危害】High concentrations of Mo can have a detrimental effect on environmental systems.

## (3) 第三篇文献标题
【生物利用性-吸附 包括Fe矿物】The mobility and bioavailability of Mo in many environmental subsurface systems.
【赤铁矿 原因】For example, hematite (α-Fe3+2O3) is a common mineral phase with high surface area.
"""

    print("🚀 开始测试文本处理功能...")
    print("\n📝 原始内容:")
    print("=" * 60)
    print(test_content)
    print("=" * 60)
    
    # 处理文本
    result = process_literature_text(test_content)
    
    print("\n📄 处理结果:")
    print("=" * 60)
    print(result)
    print("=" * 60)
    
    # 统计结果
    lines = result.split('\n')
    heading3_count = len([line for line in lines if line.startswith('###')])
    heading4_count = len([line for line in lines if line.startswith('####')])
    heading2_count = len([line for line in lines if line.startswith('## ')])
    bracket_count = len([line for line in lines if '【' in line and '】' in line])
    
    print(f"\n📊 处理结果统计:")
    print(f"   ### 标题数: {heading3_count}")
    print(f"   #### 标题数: {heading4_count}")
    print(f"   ## 标题数: {heading2_count} (应该为0)")
    print(f"   剩余【】数: {bracket_count} (应该为0)")
    
    # 验证结果
    success = (heading3_count > 0 and 
              heading4_count > 0 and 
              heading2_count == 0 and 
              bracket_count == 0)
    
    if success:
        print("\n🎉 测试通过! 功能正常工作")
    else:
        print("\n❌ 测试失败! 请检查处理逻辑")
    
    # 保存测试结果
    with open('test_result.txt', 'w', encoding='utf-8') as f:
        f.write(result)
    print(f"\n💾 结果已保存到: test_result.txt")


if __name__ == "__main__":
    main()
