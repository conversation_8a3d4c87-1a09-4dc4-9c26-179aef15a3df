'''
Author: MengjiaHe <EMAIL>
Date: 2019-02-20 19:57:17
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-14 16:52:56
FilePath: /workspace20240119 (工作区)/Users/<USER>/Documents/My_Library/inp/hmj_file/3-Resources/python脚本/rdfcn20231128.py
Description: 

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
import argparse

matplotlib.rcParams['axes.prop_cycle'] = matplotlib.cycler(color=['black'])
plt.rcParams['font.sans-serif'] = 'Helvetica'

def plot_data(data, ylabel, save_name):
    # 创建图形
    
    fig, ax = plt.subplots(figsize=(9.4/2.54, 8.5/2.54))
    ax.set_ylim(0, 3)

    ax.tick_params(axis='both', colors='black')
    ax.plot(data[:, 0], data[:, 1], '-k', linewidth=1, label='RDF')
    ax.set_ylabel('RDF')
    ax.set_xlabel('Distance (Å)')

    # 共享x轴，调整y轴刻度
    ax2 = ax.twinx()
    ax2.set_ylim(0, 4)
    ax2.set_ylabel('CN')
    ax2.plot(data[:, 0], data[:, 2], '--k', linewidth=1, label='CN')
    
    # plt.xlim(0, 4)
    # plt.xticks(np.arange(1, 6, 1))  # 9 表示结束值（不包含在内）
    # 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
    x_range=(2, 4.7)
    x_lim = 0.3
    plt.xlim(x_range)
    plt.xticks(np.arange(x_range[0], x_range[1] + x_lim, x_lim))

    plt.rcParams['svg.fonttype'] = 'none'
    plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
    # plt.rcParams.update({
        # 'font.size': 12,
        # 'font.family': 'serif',
        # 'font.serif': ['Times New Roman']
    # })
    # plt.rcParams['svg.fonttype'] = 'none'
    
    
    # plt.tight_layout()
    plt.savefig(save_name, bbox_inches='tight', format='svg')
    plt.show()

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='RDF/CN 绘图工具')
    parser.add_argument('--data', type=str, default='Od1_rdfcn.dat', help='输入数据文件名')
    args = parser.parse_args()

    # 读取数据
    a1 = np.loadtxt(args.data)
    plot_data(a1, 'CN', 'rdfcn.svg')
