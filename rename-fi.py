import os

# 定义旧文件名和新文件名的映射关系
filename_mapping = {
    "Fig5_1": "Fig6",
    "Fig6": "Fig7",
    "Fig7": "Fig8",
    "Fig8": "Fig9",
    "Fig9": "Fig10",
    "Fig10": "Fig11",
    "Fig11": "Fig12"
}

# 获取当前目录中的所有文件名
current_filenames = os.listdir('.')

# 先将文件名按照数字部分降序排序，以避免重命名时的冲突
sorted_filenames = sorted(filename_mapping.items(), key=lambda x: int(x[0][3:].split('_')[0]), reverse=True)

# 重命名文件
for old_name, new_name in sorted_filenames:
    if old_name in current_filenames:
        try:
            os.rename(old_name, new_name)
            print(f'Successfully renamed {old_name} to {new_name}')
        except Exception as e:
            print(f'Error renaming {old_name} to {new_name}: {e}')
    else:
        print(f'{old_name} does not exist in the current directory.')
