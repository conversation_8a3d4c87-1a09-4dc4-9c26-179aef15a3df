import os
import re

def replace_figure_number_in_filenames(folder_path):
    # 获取文件夹名称
    folder_name = os.path.basename(folder_path)
    
    # 提取文件夹名中的图表编号或 S 加数字
    folder_number_match = re.match(r'(Fig)\s*(\d+)', folder_name)
    if not folder_number_match:
        print(f"Folder name '{folder_name}' does not contain a valid figure or series number.")
        return
    folder_prefix = folder_number_match.group(1)  # "Fig" or "FigS"
    folder_number = folder_number_match.group(2)  # the number part

    # 定义正则表达式模式，用于匹配文件名中的图表编号或 S 加数字
    # pattern = re.compile(r'(Fig)\s*(\d+)([-_\s]\d+)?([-_v]\d+)?(\.\w+)$')
    pattern = re.compile(r'(Fig)[\s-_]*(\d+)([-_/]v\d+)?(\.\w+)$')

    # 遍历文件夹中的所有文件
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        
        # 跳过非文件
        if not os.path.isfile(file_path):
            continue
        
        # 跳过不符合命名模式的文件
        if not pattern.match(filename):
            print(f"File '{filename}' does not match the pattern. Skipping.")
            continue
            
        print(f"Processing file: {filename}")  # 添加调试信息
        
        # 使用正则表达式匹配文件名中的图表编号或 S 加数字
        match = pattern.match(filename)
        if match:
            suffix = match.group(5)  # ".ai", ".jpg", ".png" 等
            version = match.group(4) if match.group(4) else ''  # "_v2" 等版本号
            number_part = match.group(3) if match.group(3) else ''  # "-2" 或 "_1" 等数字部分
            
            # 移除多余的空格和连字符
            version = version.replace(' ', '').replace('_', '-')
            number_part = number_part.replace(' ', '').replace('_', '-')
            
            # 新文件名为文件夹名中的图表编号或 S 加数字加上原来的后缀
            new_filename = f"{folder_prefix}{folder_number}{number_part}{version}{suffix}"
            new_file_path = os.path.join(folder_path, new_filename)
            
            # 重命名文件
            os.rename(file_path, new_file_path)
            print(f"Renamed {filename} to {new_filename}")

def process_all_folders_in_directory(directory_path):
    # 遍历目录下的所有项目
    for item in os.listdir(directory_path):
        item_path = os.path.join(directory_path, item)
        # 如果是文件夹，则调用函数进行处理
        if os.path.isdir(item_path):
            print(f"Processing folder: {item_path}")  # 添加调试信息
            replace_figure_number_in_filenames(item_path)

# 获取当前代码文件的路径并设置工作目录
current_file_path = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_file_path)

# 设置 directory_path 为当前目录
directory_path = current_file_path

# 处理文件夹内文件重命名
process_all_folders_in_directory(directory_path)
