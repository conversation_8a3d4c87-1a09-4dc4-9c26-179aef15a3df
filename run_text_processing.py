#!/usr/bin/env python3
"""
文献资源库文本处理 - 运行脚本

Author: MengjiaHe <EMAIL>
Date: 2025-07-29
Description: 简化的运行脚本，直接处理指定的文本文件
"""

import sys
from pathlib import Path

# 导入改进的文本处理器
try:
    from 文献资源库_处理文本_improved import TextProcessor
except ImportError:
    print("❌ 无法导入文本处理器，请确保文件存在")
    sys.exit(1)

def main():
    """主函数 - 处理文献资源库文本"""
    
    # 输入文件
    input_file = "文献资源库-处理文本.txt"
    
    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 输入文件不存在: {input_file}")
        print("请确保文件在当前目录中")
        return
    
    print("🚀 开始处理文献资源库文本...")
    print(f"📁 输入文件: {input_file}")
    
    # 创建处理器
    processor = TextProcessor(
        input_file=input_file,
        output_file="文献资源库-处理文本_processed.txt",
        backup=True
    )
    
    # 先预览更改
    print("\n🔍 预览更改...")
    if processor.preview_changes():
        
        # 询问是否继续
        response = input("\n是否继续处理? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是', '确定']:
            print("\n🔄 开始处理...")
            
            # 执行处理
            if processor.process_all():
                print(f"\n🎉 处理完成!")
                print(f"📄 输出文件: {processor.output_file}")
                print(f"💾 备份文件: {input_file}.bak_*")
                
                # 显示处理结果摘要
                show_summary(processor.output_file)
                
            else:
                print(f"\n❌ 处理失败!")
        else:
            print("\n⏹️ 处理已取消")
    else:
        print("\n❌ 预览失败!")

def show_summary(output_file):
    """显示处理结果摘要"""
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # 统计信息
        total_lines = len(lines)
        heading_lines = len([line for line in lines if line.startswith('###')])
        empty_lines = len([line for line in lines if not line.strip()])
        content_lines = total_lines - empty_lines
        
        print(f"\n📊 处理结果摘要:")
        print(f"   总行数: {total_lines}")
        print(f"   内容行数: {content_lines}")
        print(f"   空行数: {empty_lines}")
        print(f"   ### 标题数: {heading_lines}")
        
        # 显示前几个标题
        headings = [line for line in lines if line.startswith('###')]
        if headings:
            print(f"\n📝 转换的标题示例:")
            for i, heading in enumerate(headings[:3]):
                print(f"   {i+1}. {heading[:60]}...")
            
            if len(headings) > 3:
                print(f"   ... 还有 {len(headings) - 3} 个标题")
        
    except Exception as e:
        print(f"⚠️ 无法显示摘要: {e}")

if __name__ == "__main__":
    main()
