#!/usr/bin/env python3
"""
简单测试脚本
"""

# 创建测试内容
test_content = """## (1) 第一篇文献标题
【Mo V 污染报道不常见 来源】Environmental issues with molybdenum (Mo) and vanadium (V) have not often been reported.
【都有合金的应用】Both metals are widely used in high-performance alloyed steel production.

## (2) 第二篇文献标题  
【Mo污染 饮用水标准】Molybdenum (Mo) is an emerging contaminant and at high concentration can pose a significant environmental hazard.
【Mo污染具体场景 浓度】Mo contamination can occur in a variety of scenarios from mine drainage/wastes.
"""

# 保存测试文件
with open('test_input.txt', 'w', encoding='utf-8') as f:
    f.write(test_content)

print("✅ 测试文件已创建")

# 手动实现处理逻辑进行测试
import re

def process_text(text):
    lines = text.split('\n')
    processed_lines = []
    last_heading = None
    
    # 第一步：转换【】为###，记录## 标题
    for line in lines:
        if line.startswith('## '):
            last_heading = line[3:].strip()
            processed_lines.append(line)  # 暂时保留
        else:
            # 匹配【内容】格式
            match = re.match(r'^【([^】]+)】(.*)$', line.strip())
            if match:
                bracket_content = match.group(1)
                remaining_content = match.group(2)
                new_line = f"### {bracket_content}{remaining_content}"
                processed_lines.append(new_line)
                if last_heading:
                    processed_lines.append(f"#### {last_heading}")
                    processed_lines.append("")
            else:
                processed_lines.append(line)
    
    # 第二步：删除## 行
    final_lines = [line for line in processed_lines if not line.startswith('## ')]
    
    return '\n'.join(final_lines)

# 处理测试
result = process_text(test_content)

# 保存结果
with open('test_output.txt', 'w', encoding='utf-8') as f:
    f.write(result)

print("\n📄 处理结果:")
print("=" * 50)
print(result)
print("=" * 50)

# 统计
lines = result.split('\n')
heading3_count = len([line for line in lines if line.startswith('###')])
heading4_count = len([line for line in lines if line.startswith('####')])
heading2_count = len([line for line in lines if line.startswith('## ')])

print(f"\n📊 结果统计:")
print(f"   ### 标题数: {heading3_count}")
print(f"   #### 标题数: {heading4_count}")
print(f"   ## 标题数: {heading2_count} (应该为0)")

if heading3_count > 0 and heading4_count > 0 and heading2_count == 0:
    print("\n🎉 功能测试通过!")
else:
    print("\n❌ 功能测试失败!")
