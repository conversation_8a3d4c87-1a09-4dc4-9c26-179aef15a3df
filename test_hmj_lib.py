#!/usr/bin/env python3
"""
HMJ工具库测试脚本

Author: MengjiaHe <EMAIL>
Date: 2025-07-07
Description: 测试HMJ工具库的各个模块功能

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import sys
from pathlib import Path
import numpy as np
import logging

# 添加工具库路径
sys.path.append(str(Path(__file__).parent / 'hmj-py-lib'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_config_manager():
    """测试配置管理器"""
    logger.info("🧪 测试配置管理器...")
    
    try:
        from config_manager import ConfigManager
        
        # 测试加载配置文件
        config = ConfigManager('config.yaml')
        
        # 测试获取配置
        md_config = config.get_md_config()
        plot_config = config.get_plot_config()
        
        logger.info(f"   MD配置: {md_config.get('n_processes', 'N/A')} 进程")
        logger.info(f"   绘图配置: {plot_config.get('figure_size_cm', 'N/A')} cm")
        
        # 测试嵌套键访问
        cutoff = config.get('analysis_parameters.cn_calculation.cutoff_distance', 2.5)
        logger.info(f"   截断距离: {cutoff} Å")
        
        logger.info("✅ 配置管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置管理器测试失败: {e}")
        return False


def test_data_loader():
    """测试数据加载器"""
    logger.info("🧪 测试数据加载器...")
    
    try:
        from data_loader import DataLoader
        
        data_loader = DataLoader()
        
        # 创建测试数据
        test_data = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        
        # 测试数据验证
        is_valid = data_loader.validate_data(test_data, check_finite=True)
        logger.info(f"   数据验证: {'通过' if is_valid else '失败'}")
        
        # 测试时间轴创建
        time_axis = data_loader.create_time_axis(100, timestep_fs=10.0, output_interval=0.5)
        logger.info(f"   时间轴: {len(time_axis)} 点, 范围 {time_axis[0]:.2f}-{time_axis[-1]:.2f} ps")
        
        logger.info("✅ 数据加载器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据加载器测试失败: {e}")
        return False


def test_plotting():
    """测试绘图管理器"""
    logger.info("🧪 测试绘图管理器...")
    
    try:
        from plotting import PlotManager
        from config_manager import ConfigManager
        
        config = ConfigManager('config.yaml')
        plot_manager = PlotManager(config.get_plot_config())
        
        # 创建测试数据
        x_data = np.linspace(0, 10, 100)
        y_data = np.sin(x_data) + np.random.normal(0, 0.1, 100)
        
        # 测试创建图形
        fig = plot_manager.create_figure()
        logger.info(f"   图形创建: 成功")
        
        # 测试距离vs时间绘图
        fig = plot_manager.plot_distance_vs_time(
            x_data, y_data,
            xlabel='Time (ps)',
            ylabel='Distance (Å)',
            title='Test Plot'
        )
        
        # 保存测试图形
        test_output = 'test_plot.svg'
        plot_manager.save_figure(fig, test_output)
        
        if Path(test_output).exists():
            logger.info(f"   测试图形保存: {test_output}")
            Path(test_output).unlink()  # 删除测试文件
        
        logger.info("✅ 绘图管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 绘图管理器测试失败: {e}")
        return False


def test_file_utils():
    """测试文件工具"""
    logger.info("🧪 测试文件工具...")
    
    try:
        from file_utils import FileUtils
        
        file_utils = FileUtils()
        
        # 测试文件查找
        current_dir = Path('.')
        py_files = file_utils.find_files(current_dir, "*.py", recursive=False)
        logger.info(f"   找到Python文件: {len(py_files)} 个")
        
        # 创建测试XYZ文件
        test_xyz_content = """3
Test molecule
C  0.0  0.0  0.0
H  1.0  0.0  0.0
H  0.0  1.0  0.0
"""
        test_xyz_file = Path('test_molecule.xyz')
        with open(test_xyz_file, 'w') as f:
            f.write(test_xyz_content)
        
        # 测试XYZ转换
        element_map = {'C': 'N', 'H': 'O'}
        output_path = file_utils.convert_xyz_elements(
            test_xyz_file, element_map, "_test_converted"
        )
        
        if output_path and output_path.exists():
            logger.info(f"   XYZ转换: 成功 -> {output_path.name}")
            output_path.unlink()  # 删除测试文件
        
        test_xyz_file.unlink()  # 删除测试文件
        
        logger.info("✅ 文件工具测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 文件工具测试失败: {e}")
        return False


def test_md_analysis():
    """测试分子动力学分析"""
    logger.info("🧪 测试MD分析器...")
    
    try:
        from md_analysis import MDAnalyzer
        
        md_analyzer = MDAnalyzer(n_processes=2)
        
        # 测试配位函数
        distance = 2.5
        cn_value = md_analyzer.coordination_function(distance, rc=3.0, nn=12, mm=24)
        logger.info(f"   配位函数 (d={distance}): {cn_value:.3f}")
        
        # 创建测试轨迹数据
        test_trajectory = [
            [
                ['C', 0.0, 0.0, 0.0],
                ['O', 2.0, 0.0, 0.0],
                ['O', 0.0, 2.0, 0.0]
            ],
            [
                ['C', 0.1, 0.1, 0.1],
                ['O', 2.1, 0.1, 0.1],
                ['O', 0.1, 2.1, 0.1]
            ]
        ]
        
        # 测试轨迹转换
        cell = [10.0, 10.0, 10.0, 90, 90, 90]
        pbc = (True, True, True)
        ase_trajectory = md_analyzer.trajectory_to_ase_atoms(test_trajectory, cell, pbc)
        
        logger.info(f"   轨迹转换: {len(ase_trajectory)} 帧")
        
        # 测试原子索引获取
        center_atoms = md_analyzer.get_atom_indices_by_symbol(ase_trajectory[0], 'C')
        pair_atoms = md_analyzer.get_atom_indices_by_symbol(ase_trajectory[0], 'O')
        
        logger.info(f"   中心原子: {center_atoms}, 配对原子: {pair_atoms}")
        
        logger.info("✅ MD分析器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ MD分析器测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始HMJ工具库测试...")
    
    tests = [
        ("配置管理器", test_config_manager),
        ("数据加载器", test_data_loader),
        ("绘图管理器", test_plotting),
        ("文件工具", test_file_utils),
        ("MD分析器", test_md_analysis)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试模块: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ 测试异常: {e}")
            failed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试总结")
    logger.info(f"{'='*50}")
    logger.info(f"✅ 通过: {passed} 个模块")
    logger.info(f"❌ 失败: {failed} 个模块")
    logger.info(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 所有测试通过!")
        return 0
    else:
        logger.error("⚠️ 部分测试失败，请检查错误信息")
        return 1


if __name__ == "__main__":
    sys.exit(main())
