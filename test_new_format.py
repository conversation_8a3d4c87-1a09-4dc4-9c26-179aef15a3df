#!/usr/bin/env python3
"""
测试新格式处理功能

测试将【XX】aaa转换为:
### XX
aaa
"""

import re

def test_new_format():
    """测试新的格式转换"""
    
    # 测试内容
    test_content = """## (1) 第一篇文献标题
【Mo V 污染报道不常见 来源】Environmental issues with molybdenum (Mo) and vanadium (V) have not often been reported.
【都有合金的应用】Both metals are widely used in high-performance alloyed steel production.

## (2) 第二篇文献标题  
【Mo污染 饮用水标准】Molybdenum (Mo) is an emerging contaminant and at high concentration can pose a significant environmental hazard.
【Mo污染具体场景 浓度】Mo contamination can occur in a variety of scenarios from mine drainage/wastes.
"""

    print("🚀 测试新格式处理...")
    print("\n📝 原始内容:")
    print("=" * 80)
    print(test_content)
    print("=" * 80)
    
    # 步骤1: 转换【XX】aaa为### XX + 新行aaa
    lines = test_content.split('\n')
    step1_lines = []
    last_heading = None
    converted_count = 0
    
    bracket_pattern = r'^【([^】]+)】(.*)$'
    
    for line in lines:
        if line.startswith('## '):
            last_heading = line[3:].strip()
            step1_lines.append(line)
        else:
            match = re.match(bracket_pattern, line.strip())
            if match:
                bracket_content = match.group(1)
                remaining_content = match.group(2).strip()
                
                # 转换为### 格式，另起一行放剩余内容
                step1_lines.append(f"### {bracket_content}")
                if remaining_content:
                    step1_lines.append(remaining_content)
                
                converted_count += 1
                print(f"转换: 【{bracket_content}】{remaining_content[:30]}...")
                print(f"  -> ### {bracket_content}")
                print(f"  -> {remaining_content}")
            else:
                step1_lines.append(line)
    
    print(f"\n步骤1: 转换了 {converted_count} 个【】格式")
    
    # 步骤2: 在### 后添加#### 标题
    step2_lines = []
    last_heading = None
    copied_count = 0
    
    for line in step1_lines:
        if line.startswith('## '):
            last_heading = line[3:].strip()
            step2_lines.append(line)
        elif line.startswith('###'):
            step2_lines.append(line)
            if last_heading:
                step2_lines.append(f"#### {last_heading}")
                copied_count += 1
            step2_lines.append("")  # 空行分隔
        else:
            step2_lines.append(line)
    
    print(f"步骤2: 复制了 {copied_count} 个标题为#### 格式")
    
    # 步骤3: 删除## 行
    final_lines = []
    removed_count = 0
    
    for line in step2_lines:
        if line.startswith('## '):
            removed_count += 1
        else:
            final_lines.append(line)
    
    print(f"步骤3: 删除了 {removed_count} 个## 标题行")
    
    result = '\n'.join(final_lines)
    
    print("\n📄 最终结果:")
    print("=" * 80)
    print(result)
    print("=" * 80)
    
    # 统计验证
    result_lines = result.split('\n')
    heading3_count = len([line for line in result_lines if line.startswith('###')])
    heading4_count = len([line for line in result_lines if line.startswith('####')])
    heading2_count = len([line for line in result_lines if line.startswith('## ')])
    bracket_count = len([line for line in result_lines if '【' in line and '】' in line])
    
    print(f"\n📊 结果统计:")
    print(f"   ### 标题数: {heading3_count}")
    print(f"   #### 标题数: {heading4_count}")
    print(f"   ## 标题数: {heading2_count} (应该为0)")
    print(f"   剩余【】数: {bracket_count} (应该为0)")
    
    # 检查格式是否正确
    success = True
    for i, line in enumerate(result_lines):
        if line.startswith('###'):
            # 检查下一行是否是#### 格式
            if i + 1 < len(result_lines) and result_lines[i + 1].startswith('####'):
                print(f"✅ {line} -> {result_lines[i + 1]}")
            else:
                print(f"❌ {line} 后面没有正确的#### 行")
                success = False
    
    if success and heading2_count == 0 and bracket_count == 0:
        print("\n🎉 测试通过! 新格式处理正常")
    else:
        print("\n❌ 测试失败! 请检查处理逻辑")
    
    # 保存结果
    with open('test_new_format_result.txt', 'w', encoding='utf-8') as f:
        f.write(result)
    print(f"\n💾 结果已保存到: test_new_format_result.txt")

if __name__ == "__main__":
    test_new_format()
