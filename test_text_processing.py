#!/usr/bin/env python3
"""
测试文本处理功能

Author: MengjiaHe <EMAIL>
Date: 2025-07-29
Description: 测试【XX】转换为### XX，以及## XX复制为#### XX的功能
"""

def create_test_file():
    """创建测试文件"""
    test_content = """
## (1) 第一篇文献标题
【Mo V 污染报道不常见 来源】Environmental issues with molybdenum (Mo) and vanadium (V) have not often been reported.
【都有合金的应用】Both metals are widely used in high-performance alloyed steel production.
【V应用】Future developments in vanadium usage, such as the growing adoption of V-based sodium-ion batteries.

## (2) 第二篇文献标题  
【Mo污染 饮用水标准】Molybdenum (Mo) is an emerging contaminant and at high concentration can pose a significant environmental hazard.
【Mo污染具体场景 浓度】Mo contamination can occur in a variety of scenarios from mine drainage/wastes.
【Mo 具体危害】High concentrations of Mo can have a detrimental effect on environmental systems.

## (3) 第三篇文献标题
【生物利用性-吸附 包括Fe矿物】The mobility and bioavailability of Mo in many environmental subsurface systems.
【赤铁矿 原因】For example, hematite (α-Fe3+2O3) is a common mineral phase with high surface area.
"""
    
    with open('test_input.txt', 'w', encoding='utf-8') as f:
        f.write(test_content.strip())
    
    print("✅ 测试文件已创建: test_input.txt")

def run_test():
    """运行测试"""
    from 文献资源库_处理文本_improved import TextProcessor
    
    # 创建测试文件
    create_test_file()
    
    # 创建处理器
    processor = TextProcessor(
        input_file='test_input.txt',
        output_file='test_output.txt',
        backup=False
    )
    
    print("\n🔍 预览测试文件...")
    processor.preview_changes()
    
    print("\n🔄 开始处理...")
    if processor.process_all():
        print("\n✅ 处理成功!")
        
        # 显示结果
        with open('test_output.txt', 'r', encoding='utf-8') as f:
            result = f.read()
        
        print("\n📄 处理结果:")
        print("=" * 50)
        print(result)
        print("=" * 50)
        
        # 验证结果
        lines = result.split('\n')
        heading3_count = len([line for line in lines if line.startswith('###')])
        heading4_count = len([line for line in lines if line.startswith('####')])
        heading2_count = len([line for line in lines if line.startswith('## ')])
        
        print(f"\n📊 结果统计:")
        print(f"   ### 标题数: {heading3_count}")
        print(f"   #### 标题数: {heading4_count}")
        print(f"   ## 标题数: {heading2_count} (应该为0)")
        
        if heading3_count > 0 and heading4_count > 0 and heading2_count == 0:
            print("\n🎉 测试通过!")
        else:
            print("\n❌ 测试失败!")
    
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    run_test()
