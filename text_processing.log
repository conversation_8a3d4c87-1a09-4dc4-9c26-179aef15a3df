2025-07-29 16:30:58,610 - INFO - 📝 文本处理器初始化完成
2025-07-29 16:30:58,610 - INFO -    输入文件: 文献资源库-处理文本.txt
2025-07-29 16:30:58,610 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:30:58,610 - INFO - 🚀 开始文本处理...
2025-07-29 16:30:58,611 - INFO - ✅ 成功读取文件: 文献资源库-处理文本.txt
2025-07-29 16:30:58,611 - INFO -    文件大小: 203404 字符
2025-07-29 16:30:58,612 - INFO -    行数: 855 行
2025-07-29 16:30:58,612 - INFO - 💾 备份文件已创建: 文献资源库-处理文本.bak_20250729_163058
2025-07-29 16:30:58,612 - INFO - 🔄 步骤1完成: 转换了 0 个 '000-' 行为 '###' 格式
2025-07-29 16:30:58,612 - INFO - 🔄 步骤2完成: 复制了 1 个标题内容
2025-07-29 16:30:58,612 - INFO - 🔄 步骤3完成: 删除了 33 个 '## ' 标题行
2025-07-29 16:30:58,613 - INFO - ✅ 文本处理完成!
2025-07-29 16:30:58,613 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:30:58,613 - INFO -    输出大小: 193309 字符
2025-07-29 16:30:58,613 - INFO -    输出行数: 824 行
2025-07-29 16:38:00,598 - INFO - 📝 文本处理器初始化完成
2025-07-29 16:38:00,599 - INFO -    输入文件: 文献资源库-处理文本.txt
2025-07-29 16:38:00,599 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:38:00,599 - INFO - 🚀 开始文本处理...
2025-07-29 16:38:00,599 - INFO - ✅ 成功读取文件: 文献资源库-处理文本.txt
2025-07-29 16:38:00,599 - INFO -    文件大小: 203404 字符
2025-07-29 16:38:00,599 - INFO -    行数: 855 行
2025-07-29 16:38:00,600 - INFO - 💾 备份文件已创建: 文献资源库-处理文本.bak_20250729_163800
2025-07-29 16:38:00,601 - INFO - 🔄 步骤1完成: 转换了 738 个 '【】' 格式为 '###' 格式
2025-07-29 16:38:00,601 - INFO - 🔄 步骤2完成: 复制了 739 个标题内容为 '#### ' 格式
2025-07-29 16:38:00,602 - INFO - 🔄 步骤3完成: 删除了 33 个 '## ' 标题行
2025-07-29 16:38:00,602 - INFO - ✅ 文本处理完成!
2025-07-29 16:38:00,602 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:38:00,602 - INFO -    输出大小: 431956 字符
2025-07-29 16:38:00,602 - INFO -    输出行数: 2300 行
2025-07-29 16:39:52,676 - INFO - 📝 文本处理器初始化完成
2025-07-29 16:39:52,676 - INFO -    输入文件: 文献资源库-处理文本.txt
2025-07-29 16:39:52,676 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:39:52,676 - INFO - 🚀 开始文本处理...
2025-07-29 16:39:52,677 - INFO - ✅ 成功读取文件: 文献资源库-处理文本.txt
2025-07-29 16:39:52,677 - INFO -    文件大小: 203404 字符
2025-07-29 16:39:52,677 - INFO -    行数: 855 行
2025-07-29 16:39:52,678 - INFO - 💾 备份文件已创建: 文献资源库-处理文本.bak_20250729_163952
2025-07-29 16:39:52,678 - INFO - 🔄 步骤1完成: 转换了 738 个 '【】' 格式为 '###' 格式
2025-07-29 16:39:52,679 - INFO - 🔄 步骤2完成: 复制了 739 个标题内容为 '#### ' 格式
2025-07-29 16:39:52,679 - INFO - 🔄 步骤3完成: 删除了 33 个 '## ' 标题行
2025-07-29 16:39:52,680 - INFO - ✅ 文本处理完成!
2025-07-29 16:39:52,680 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:39:52,680 - INFO -    输出大小: 431956 字符
2025-07-29 16:39:52,680 - INFO -    输出行数: 2300 行
2025-07-29 16:42:12,450 - INFO - 📝 文本处理器初始化完成
2025-07-29 16:42:12,450 - INFO -    输入文件: 文献资源库-处理文本.txt
2025-07-29 16:42:12,450 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:42:12,450 - INFO - 🚀 开始文本处理...
2025-07-29 16:42:12,451 - INFO - ✅ 成功读取文件: 文献资源库-处理文本.txt
2025-07-29 16:42:12,451 - INFO -    文件大小: 203404 字符
2025-07-29 16:42:12,451 - INFO -    行数: 855 行
2025-07-29 16:42:12,451 - INFO - 💾 备份文件已创建: 文献资源库-处理文本.bak_20250729_164212
2025-07-29 16:42:12,452 - INFO - 🔄 步骤1完成: 转换了 738 个 '【】' 格式为 '###' 格式，内容另起一行
2025-07-29 16:42:12,452 - INFO - 🔄 步骤2完成: 复制了 739 个标题内容为 '#### ' 格式
2025-07-29 16:42:12,453 - INFO - 🔄 步骤3完成: 删除了 33 个 '## ' 标题行
2025-07-29 16:42:12,454 - INFO - ✅ 文本处理完成!
2025-07-29 16:42:12,454 - INFO -    输出文件: 文献资源库-处理文本_processed.txt
2025-07-29 16:42:12,454 - INFO -    输出大小: 432693 字符
2025-07-29 16:42:12,454 - INFO -    输出行数: 3038 行
