'''
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2023-10-23 15:49:41
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2023-11-26 23:02:01
FilePath: /undefined/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/My_Library/inp/hmj_inp/Matlab_python_hmj/python/作图小技巧.py
Description: python作图小技巧

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''


#x.y轴
ax2.tick_params(axis='x', which='both', bottom=True, top=False, labelbottom=True, labeltop=False, pad=15)
ax.tick_params(axis='x', which='both', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)
ax.tick_params(axis='y', which='both', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)

ax.tick_params(axis='both', which='both', bottom=True, top=True, labelbottom=True, labeltop=True, pad=15)

#刻度线朝内 xy顶底都有刻度线
ax.tick_params(axis='both', which='both', direction='in', bottom=True, top=True, left=True, right=True, labelbottom=True, labeltop=False, labelleft=True, labelright=False, pad=5)

#xy轴的范围
ax.set_xlim(0, 10)

# 下标和Å
ax.set_ylabel(r'W-O$_{\mathrm{H2O}}$ Distance ($\AA$)')

# 图的大小 设置相对的A4纸张大小 1*3 时的：
paper_width = 210 / 25.4 / 1.1 /2.5  # 宽度，单位为英寸 1/3的A4
paper_height = 297 / 25.4 / 2/2.5  # 高度，单位为英寸

# 图的字体
plt.rcParams['font.size'] = 12
matplotlib.rcParams['font.family'] = 'Helvetica'
plt.rcParams['svg.fonttype'] = 'none'
plt.savefig('figure_test.svg',bbox_inches='tight', format='svg')

