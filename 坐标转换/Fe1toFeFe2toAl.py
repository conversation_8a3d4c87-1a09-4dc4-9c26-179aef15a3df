'''
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2025-01-14 16:05:41
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2025-01-16 15:30:58
FilePath: /undefined/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/Goe_opt/Fe1toFeFe2toAl.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
用VMD将水去掉：
name Fe Al W || (name O && (within 2.5 of (name Fe Al  W)))|| (name H && within 1.2 of (name O && (within 2.5 of (name Fe Al W))))
'''
import os

def modify_xyz_files():
    # 获取脚本所在路径
    script_dir = os.getcwd()
    
    # 遍历脚本目录中的所有文件
    for filename in os.listdir(script_dir):
        # 只处理 .xyz 文件
        if filename.endswith('.xyz'):
            input_file = os.path.join(script_dir, filename)
            output_file = os.path.join(script_dir, filename.replace('.xyz', '_modified.xyz'))
            
            # 打开并读取文件内容
            with open(input_file, 'r') as file:
                lines = file.readlines()
            
            # 修改内容
            modified_lines = []
            for line in lines:
                # 替换 Fe1 为 Fe，Fe2 为 Al
                modified_line = line.replace('Fe1', 'Fe').replace('Fe2', 'Al')
                modified_lines.append(modified_line)
            
            # 保存为新文件
            with open(output_file, 'w') as file:
                file.writelines(modified_lines)
            
            print(f"Modified file saved as: {output_file}")

if __name__ == "__main__":
    modify_xyz_files()