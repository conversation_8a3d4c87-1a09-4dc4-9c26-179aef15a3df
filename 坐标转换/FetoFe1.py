'''
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2025-01-14 16:05:41
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2025-01-16 15:39:45
FilePath: /undefined/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/largebox/FetoFe1.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import re

def modify_xyz_file(input_file, output_file):
    """修改 .xyz 文件，将第一列中的 Fe 改为 Fe1，Al 改为 Fe2"""
    # 读取输入文件内容
    with open(input_file, "r") as file:
        lines = file.readlines()

    new_lines = []
    for line in lines:
        # 使用正则表达式处理第一列（可能前面有空格或 Tab）
        new_line = re.sub(r'^\s*Fe\s+', 'Fe1 ', line)  # Fe 改为 Fe1
        new_line = re.sub(r'^\s*Al\s+', 'Fe2 ', new_line)  # Al 改为 Fe2
        new_lines.append(new_line)

    # 写入输出文件内容
    with open(output_file, "w") as file:
        file.writelines(new_lines)

    # 输出修改后的文件内容
    print(f"修改完成！结果保存在 {output_file} 中。\n")
    print("修改后的文件内容如下：\n")
    with open(output_file, "r") as file:
        print(file.read())  # 打印文件内容


# 调用函数
input_file = "5CN.xyz"  # 输入文件路径
output_file = "5CN_2.xyz"  # 输出文件路径
modify_xyz_file(input_file, output_file)