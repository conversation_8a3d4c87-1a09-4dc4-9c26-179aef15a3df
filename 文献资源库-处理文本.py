'''
Author: MengjiaHe <EMAIL>
Date: 2023-11-22 13:52:24
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-29 12:06:25
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/Paper/W paper/处理文本.py
Description: 你想要处理文本中以"000-"开头的段落，删除这个前缀并在段落开头添加"###"。
将每个以"###"开头的段落后面复制它前面最近的第一个以"## "开头的段落的内容
Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import os
import re

def read_md_file_in_directory(file_name):
    script_directory = os.path.dirname(os.path.realpath(__file__))  # 获取当前脚本所在目录路径
    file_path = os.path.join(script_directory, file_name)  # 构建文件的完整路径

    if os.path.exists(file_path) and file_path.endswith('.txt'):
        with open(file_path, 'r', encoding='utf-8') as md_file:
            content = md_file.read()
            return content
    else:
        return None  # 如果文件不存在或不是Markdown文件，则返回空

def process_text(text):
    lines = text.split('\n')  # 将文本按行分割成列表

    processed_lines = []
    for line in lines:
        if line.startswith('000-'):
            # 删除000-并在前面加上###
            processed_lines.append(f"### {line[4:]}")
        else:
            processed_lines.append(line)

    processed_text = '\n'.join(processed_lines)  # 将处理后的行重新连接成文本
    return processed_text

def copy_content_to_heading(text):
    lines = text.split('\n')  # 将文本按行分割成列表

    processed_lines = []
    last_heading = None  # 存储最近的"## "开头的段落内容
    for line in lines:
        if line.startswith('## '):
            last_heading = line[3:]  # 更新最近的"## "开头的段落内容
        elif line.startswith('###'):
            if last_heading:
                # 将最近的"## "开头的段落内容添加到"###"开头的段落后面
                line = f"{line}\n{last_heading}\n"

        # 添加处理后的行
        processed_lines.append(line)

    processed_text = '\n'.join(processed_lines)  # 将处理后的行重新连接成文本
    return processed_text

def remove_specific_heading_lines(text):
    lines = text.split('\n')  # 将文本按行分割成列表

    processed_lines = []
    for line in lines:
        if not line.startswith('## '):
            processed_lines.append(line)  # 如果不以"## "开头，就保留该行内容

    processed_text = '\n'.join(processed_lines)  # 将处理后的行重新连接成文本
    return processed_text




# 用法示例
file_name_to_read = '图.txt'  # 替换为你要读取的Markdown文件名
md_content = read_md_file_in_directory(file_name_to_read)

step1 = process_text(md_content)
# print(processed_text)
output_file_path = "step1.txt"  # 新建的Markdown文件名
with open(output_file_path, "w", encoding="utf-8") as file:
    file.write(step1)
    
step2 = copy_content_to_heading(step1)
# print(step2)
output_file_path = "step2.txt"  # 新建的Markdown文件名
with open(output_file_path, "w", encoding="utf-8") as file:
    file.write(step2)

file_name_to_read = 'step2.txt'  # 替换为你要读取的Markdown文件名
md_content = read_md_file_in_directory(file_name_to_read)

# step3 = remove_specific_heading_lines(md_content)
step3 = remove_specific_heading_lines(step2)

output_file_path = "step3.txt"  # 新建的Markdown文件名
with open(output_file_path, "w", encoding="utf-8") as file:
    file.write(step3)

print(f"处理后的文本已写入 {output_file_path}")

# if md_content:
#     print("文件内容:")
#     print(md_content)
# else:
#     print("未找到文件或文件格式不正确")
