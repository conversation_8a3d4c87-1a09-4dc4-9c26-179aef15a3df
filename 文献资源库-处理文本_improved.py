#!/usr/bin/env python3
"""
文献资源库文本处理脚本 (改进版)

Author: MengjiaHe <EMAIL>
Date: 2025-07-29
Description: 处理文献资源库文本，将"【XX】"开头的段落转换为"### XX"格式，
            并将前面最近的"## XX"内容复制为"#### XX"格式，最后删除所有"## "行

功能:
1. 将"【XX】"开头的行转换为"### XX"格式
2. 在每个"###"行后添加前面最近的"## "行内容，转换为"#### "格式
3. 删除所有"## "开头的行
4. 支持自定义输入输出文件
5. 添加备份和错误处理

Copyright (c) 2025 by MengjiaHe, All Rights Reserved.
"""

import os
import sys
from pathlib import Path
import argparse
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('text_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TextProcessor:
    """文本处理器类"""
    
    def __init__(self, input_file: str, output_file: str = None, backup: bool = True):
        """
        初始化文本处理器
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径，默认为输入文件名_processed.txt
            backup: 是否创建备份文件
        """
        self.input_file = Path(input_file)
        
        if output_file:
            self.output_file = Path(output_file)
        else:
            self.output_file = self.input_file.with_stem(f"{self.input_file.stem}_processed")
        
        self.backup = backup
        self.content = ""
        
        logger.info(f"📝 文本处理器初始化完成")
        logger.info(f"   输入文件: {self.input_file}")
        logger.info(f"   输出文件: {self.output_file}")
    
    def read_file(self) -> bool:
        """
        读取输入文件
        
        Returns:
            bool: 是否成功读取
        """
        try:
            if not self.input_file.exists():
                logger.error(f"❌ 输入文件不存在: {self.input_file}")
                return False
            
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.content = f.read()
            
            logger.info(f"✅ 成功读取文件: {self.input_file}")
            logger.info(f"   文件大小: {len(self.content)} 字符")
            logger.info(f"   行数: {len(self.content.splitlines())} 行")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 读取文件失败: {e}")
            return False
    
    def create_backup(self) -> bool:
        """
        创建备份文件
        
        Returns:
            bool: 是否成功创建备份
        """
        if not self.backup:
            return True
        
        try:
            backup_file = self.input_file.with_suffix(f".bak_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            backup_file.write_text(self.content, encoding='utf-8')
            
            logger.info(f"💾 备份文件已创建: {backup_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {e}")
            return False
    
    def process_brackets_to_heading(self, text: str) -> str:
        """
        将开头的【XX】格式转换为"### XX"格式

        Args:
            text: 输入文本

        Returns:
            str: 处理后的文本
        """
        import re
        lines = text.split('\n')
        processed_lines = []
        converted_count = 0

        # 匹配行开头的【内容】格式
        bracket_pattern = r'^【([^】]+)】(.*)$'

        for line in lines:
            match = re.match(bracket_pattern, line.strip())
            if match:
                # 提取【】内的内容和后面的内容
                bracket_content = match.group(1)
                remaining_content = match.group(2)

                # 转换为### 格式
                new_line = f"### {bracket_content}{remaining_content}"
                processed_lines.append(new_line)
                converted_count += 1
                logger.debug(f"转换: 【{bracket_content}】... -> ### {bracket_content}...")
            else:
                processed_lines.append(line)

        logger.info(f"🔄 步骤1完成: 转换了 {converted_count} 个 '【】' 格式为 '###' 格式")
        return '\n'.join(processed_lines)
    
    def copy_heading_content(self, text: str) -> str:
        """
        在每个"###"行后添加前面最近的"## "行内容，并转换为"#### "格式

        Args:
            text: 输入文本

        Returns:
            str: 处理后的文本
        """
        lines = text.split('\n')
        processed_lines = []
        last_heading = None
        copied_count = 0

        for line in lines:
            if line.startswith('## '):
                # 更新最近的"## "行内容（去掉"## "前缀）
                last_heading = line[3:].strip()
                logger.debug(f"记录标题: {last_heading[:30]}...")
                processed_lines.append(line)  # 先保留原行，后面会删除

            elif line.startswith('###'):
                # 添加"###"行
                processed_lines.append(line)
                if last_heading:
                    # 在"###"行后添加最近的标题内容，转换为"#### "格式
                    processed_lines.append(f"#### {last_heading}")
                    copied_count += 1
                    logger.debug(f"复制标题到: {line[:30]}... -> #### {str(last_heading)[:20]}...")
                processed_lines.append("")  # 添加空行分隔
            else:
                processed_lines.append(line)

        logger.info(f"🔄 步骤2完成: 复制了 {copied_count} 个标题内容为 '#### ' 格式")
        return '\n'.join(processed_lines)
    
    def remove_heading_lines(self, text: str) -> str:
        """
        删除所有"## "开头的行
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        lines = text.split('\n')
        processed_lines = []
        removed_count = 0
        
        for line in lines:
            if line.startswith('## '):
                removed_count += 1
                logger.debug(f"删除标题行: {line[:50]}...")
            else:
                processed_lines.append(line)
        
        logger.info(f"🔄 步骤3完成: 删除了 {removed_count} 个 '## ' 标题行")
        return '\n'.join(processed_lines)
    
    def process_all(self) -> bool:
        """
        执行完整的文本处理流程
        
        Returns:
            bool: 是否处理成功
        """
        try:
            logger.info("🚀 开始文本处理...")
            
            # 读取文件
            if not self.read_file():
                return False
            
            # 创建备份
            if not self.create_backup():
                logger.warning("⚠️ 备份创建失败，继续处理...")
            
            # 步骤1: 转换"【】"为"###"
            step1_result = self.process_brackets_to_heading(self.content)
            
            # 步骤2: 复制标题内容
            step2_result = self.copy_heading_content(step1_result)
            
            # 步骤3: 删除"## "行
            final_result = self.remove_heading_lines(step2_result)
            
            # 保存结果
            self.output_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write(final_result)
            
            logger.info(f"✅ 文本处理完成!")
            logger.info(f"   输出文件: {self.output_file}")
            logger.info(f"   输出大小: {len(final_result)} 字符")
            logger.info(f"   输出行数: {len(final_result.splitlines())} 行")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 文本处理失败: {e}")
            return False
    
    def preview_changes(self) -> bool:
        """
        预览将要进行的更改
        
        Returns:
            bool: 是否成功预览
        """
        try:
            if not self.read_file():
                return False
            
            lines = self.content.split('\n')
            
            # 统计信息
            import re
            heading_lines = [i for i, line in enumerate(lines, 1) if line.startswith('## ')]
            bracket_pattern = r'^【([^】]+)】'
            bracket_lines = [i for i, line in enumerate(lines, 1) if re.match(bracket_pattern, line.strip())]

            logger.info("🔍 预览更改:")
            logger.info(f"   找到 {len(heading_lines)} 个 '## ' 标题行")
            logger.info(f"   找到 {len(bracket_lines)} 个 '【】' 行需要转换")

            if heading_lines:
                logger.info("   '## ' 标题行示例:")
                for line_num in heading_lines[:3]:
                    logger.info(f"     第{line_num}行: {lines[line_num-1][:60]}...")

            if bracket_lines:
                logger.info("   '【】' 行示例:")
                for line_num in bracket_lines[:3]:
                    logger.info(f"     第{line_num}行: {lines[line_num-1][:60]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 预览失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='文献资源库文本处理工具 (改进版)')
    parser.add_argument('input_file', help='输入文本文件路径')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--no-backup', action='store_true', help='不创建备份文件')
    parser.add_argument('--preview', action='store_true', help='预览更改而不实际处理')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 创建处理器
        processor = TextProcessor(
            input_file=args.input_file,
            output_file=args.output,
            backup=not args.no_backup
        )
        
        if args.preview:
            # 预览模式
            if processor.preview_changes():
                print("\n🔍 预览完成! 使用 --help 查看处理选项")
            else:
                print("\n❌ 预览失败!")
                sys.exit(1)
        else:
            # 处理模式
            if processor.process_all():
                print(f"\n🎉 文本处理成功!")
                print(f"输出文件: {processor.output_file}")
            else:
                print(f"\n❌ 文本处理失败!")
                sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
