'''
Author: MengjiaHe <EMAIL>
Date: 2023-10-23 15:49:41
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2023-12-01 14:33:29
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Matlab_python_hmj/hmj-python-code/画图小技巧1130.py
Description: 

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
# 制定字体和大小
plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})

# 创建一个指定尺寸的图（单位为厘米）
# 对于单栏宽度（在文章中占一列），一般在 8 厘米至 9 厘米之间。
# 双栏宽度（在文章中占两列）通常在 17 厘米至 18 厘米之间。
# 三栏 6cm？
# plt.figure(figsize=(width_inches, height_inches))

# 三栏1/3: 6cm
plt.figure(figsize=(8/2.54, 6/2.54))
plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
# 两栏1/2: 8cm
plt.figure(figsize=(8/2.54, 10/2.54))
# 一栏: 10cm
plt.figure(figsize=(12/2.54, 10/2.54))


#PPT 16:9 33.867 厘米 × 19.05 厘米 * 70%-90%
plt.figure(figsize=(33.867/2.54*0.7, 19.05/2.54*0.7))
# 4:3尺寸：25.4 厘米 × 19.05 厘米
plt.figure(figsize=(25.4/2.54*0.7, 19.05/2.54*0.7))


# 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
# 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
x_range=(0, 12)
plt.xlim(x_range)
plt.xticks(np.arange(x_range[0], x_range[1] + 1, 2))

y_range=(0, 40)
plt.ylim(y_range)
plt.yticks(np.arange(y_range[0], y_range[1] + 1, 2))
        
x2_ticks = np.arange(2.2, 4.8, 0.4)
ax2.set_xticks(x2_ticks)
ax2.set_xticklabels([f'{t:.1f}' for t in x2_ticks])


plt.rcParams['svg.fonttype'] = 'none'
plt.savefig('P.svg', bbox_inches='tight', format='svg')
plt.show()