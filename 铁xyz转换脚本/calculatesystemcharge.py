import os

# 获取当前工作目录
script_dir = os.path.dirname(os.path.abspath(__file__))
print(f"脚本所在目录是: {script_dir}")

# 定义.xyz文件的路径
xyz_file = os.path.join(script_dir, 'large-box_W_noperiod.xyz')

# 定义元素电荷
charges = {
    'H': 1,
    'O': -2,
    'Fe': 3,
    'Al': 3,
    'W': 6
}

# 初始化总电荷
total_charge = 0

# 读取并解析 xyz 文件
try:
    with open(xyz_file, 'r') as file:
        lines = file.readlines()[2:]  # 跳过前两行标题

        # 解析文件内容并计算总电荷
        for line in lines:
            parts = line.split()
            if len(parts) > 0:
                element = parts[0]
                if element in charges:
                    total_charge += charges[element]
                else:
                    print(f"警告: 找到未知元素 {element}, 无法计算电荷。")

    print(f"Total charge of the system: {total_charge}")

except FileNotFoundError:
    print(f"错误: 文件 {xyz_file} 未找到，请检查路径。")
except Exception as e:
    print(f"发生错误: {e}")