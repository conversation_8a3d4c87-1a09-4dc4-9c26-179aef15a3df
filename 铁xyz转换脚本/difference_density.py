import numpy as np

def read_cube(filename):
    with open(filename, 'r') as f:
        lines = f.readlines()
    header = lines[:6]
    data = np.array([float(x) for x in ''.join(lines[6:]).split()])
    return header, data

def write_cube(filename, header, data):
    with open(filename, 'w') as f:
        f.writelines(header)
        for value in data:
            f.write(f'{value:.5e} ')
            if (data.tolist().index(value) + 1) % 6 == 0:
                f.write('\n')

header_before, data_before = read_cube('before_adsorption.cub')
header_after, data_after = read_cube('after_adsorption.cub')

difference_data = data_after - data_before

write_cube('difference_density.cub', header_before, difference_data)