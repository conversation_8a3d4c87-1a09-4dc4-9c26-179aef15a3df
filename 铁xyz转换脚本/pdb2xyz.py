'''
Author: MengjiaHe <EMAIL>
Date: 2025-01-15 16:12:09
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-01-15 16:12:34
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/largebox/pdb2xyz.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
def extract_atom_lines(pdb_file):
    atom_lines = []

    with open(pdb_file, 'r') as f:
        for line in f:
            if line.startswith('ATOM'):
                atom_lines.append(line)

    return atom_lines

def convert_pdb_to_xyz(pdb_file, xyz_file):
    atom_lines = extract_atom_lines(pdb_file)

    with open(xyz_file, 'w') as f:
        f.write(f"{len(atom_lines)}\n")
        f.write("Converted from PDB file\n")

        for line in atom_lines:
            atom_name = line[12:16].strip()
            x = float(line[30:38])
            y = float(line[38:46])
            z = float(line[46:54])

            f.write(f"{atom_name}       {x:.3f} {y:.3f} {z:.3f}\n")

    print("Conversion completed!")

# 指定PDB文件路径和输出的XYZ文件路径
pdb_file = '/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/largebox/packmol_input/28.pdb'
xyz_file = '/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/largebox/packmol_input/28.xyz'

# 调用函数进行提取和转换
convert_pdb_to_xyz(pdb_file, xyz_file)